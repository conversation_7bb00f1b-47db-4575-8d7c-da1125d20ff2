import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import DownCircleFilledSvg from "@ant-design/icons-svg/es/asn/DownCircleFilled";
import AntdIcon from "../components/AntdIcon";
var DownCircleFilled = function DownCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: DownCircleFilledSvg
  }));
};

/**![down-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0xODQuNSAzNTMuN2wtMTc4IDI0NmE3Ljk1IDcuOTUgMCAwMS0xMi45IDBsLTE3OC0yNDZjLTMuOC01LjMgMC0xMi43IDYuNS0xMi43SDM4MWMxMC4yIDAgMTkuOSA0LjkgMjUuOSAxMy4yTDUxMiA1NjMuNmwxMDUuMi0xNDUuNGM2LTguMyAxNS42LTEzLjIgMjUuOS0xMy4ySDY5MGM2LjUgMCAxMC4zIDcuNCA2LjUgMTIuN3oiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(DownCircleFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'DownCircleFilled';
}
export default RefIcon;