// 模板相关类型定义

import { ActionConfiguration, PluginType, ActionType } from './action';

export interface QueryTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  pluginType: PluginType;
  actionType: ActionType;
  template: string;
  variables: TemplateVariable[];
  tags: string[];
  isPublic: boolean;
  usageCount: number;
  rating: number;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface TemplateVariable {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'select' | 'multiselect';
  label: string;
  description?: string;
  defaultValue?: any;
  required: boolean;
  options?: TemplateVariableOption[];
  validation?: {
    pattern?: string;
    min?: number;
    max?: number;
    message?: string;
  };
}

export interface TemplateVariableOption {
  label: string;
  value: any;
  description?: string;
}

export interface TemplateCategory {
  id: string;
  name: string;
  description: string;
  icon?: string;
  parentId?: string;
  children?: TemplateCategory[];
  templateCount: number;
}

export interface TemplateApplication {
  templateId: string;
  variables: Record<string, any>;
  targetActionId?: string;
}

export interface TemplateApplicationResult {
  success: boolean;
  actionConfiguration: ActionConfiguration;
  errors?: string[];
  warnings?: string[];
}

export interface TemplateCreateRequest {
  name: string;
  description: string;
  category: string;
  pluginType: PluginType;
  actionType: ActionType;
  template: string;
  variables: TemplateVariable[];
  tags: string[];
  isPublic: boolean;
}

export interface TemplateUpdateRequest {
  name?: string;
  description?: string;
  category?: string;
  template?: string;
  variables?: TemplateVariable[];
  tags?: string[];
  isPublic?: boolean;
}

export interface TemplateSearchFilters {
  query?: string;
  category?: string;
  pluginType?: PluginType;
  actionType?: ActionType;
  tags?: string[];
  isPublic?: boolean;
  createdBy?: string;
  rating?: number;
}

export interface TemplateUsageStats {
  templateId: string;
  usageCount: number;
  uniqueUsers: number;
  averageRating: number;
  ratingCount: number;
  lastUsed: string;
  popularVariables: Record<string, any>;
}

export interface TemplateRating {
  id: string;
  templateId: string;
  userId: string;
  rating: number;
  comment?: string;
  createdAt: string;
}

export interface TemplateComment {
  id: string;
  templateId: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  content: string;
  parentId?: string;
  replies?: TemplateComment[];
  likes: number;
  isLiked: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface TemplateShare {
  id: string;
  templateId: string;
  shareType: 'link' | 'email' | 'organization';
  permissions: 'view' | 'use' | 'edit';
  expiresAt?: string;
  accessCount: number;
  createdBy: string;
  createdAt: string;
}

export interface TemplateImportRequest {
  source: 'file' | 'url' | 'text';
  data: string;
  format: 'json' | 'yaml';
  overwriteExisting?: boolean;
}

export interface TemplateExportRequest {
  templateIds: string[];
  format: 'json' | 'yaml';
  includeMetadata?: boolean;
  includeUsageStats?: boolean;
}
