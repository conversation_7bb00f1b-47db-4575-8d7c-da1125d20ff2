import React from 'react';
import { Outlet } from 'react-router-dom';
import { Layout } from 'antd';
import styled from 'styled-components';

import { useAppSelector } from '@/store';
import Sidebar from './Sidebar';
import Header from './Header';
import Footer from './Footer';

const { Content } = Layout;

// 样式组件
const StyledLayout = styled(Layout)`
  min-height: 100vh;
  background: var(--dataset-bg-secondary);
`;

const StyledSider = styled(Layout.Sider)`
  background: var(--dataset-bg) !important;
  border-right: 1px solid var(--dataset-border);
  
  .ant-layout-sider-trigger {
    background: var(--dataset-bg-secondary);
    border-top: 1px solid var(--dataset-border);
    color: var(--dataset-text);
    
    &:hover {
      background: var(--dataset-bg-disabled);
    }
  }
`;

const StyledContent = styled(Content)`
  background: var(--dataset-bg-secondary);
  display: flex;
  flex-direction: column;
  overflow: hidden;
`;

const ContentWrapper = styled.div`
  flex: 1;
  padding: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
`;

const MainContent = styled.div`
  flex: 1;
  overflow: auto;
  background: var(--dataset-bg-secondary);
`;

// 主布局组件
const MainLayout: React.FC = () => {
  const { sidebarCollapsed, sidebarWidth } = useAppSelector(state => state.ui);

  return (
    <StyledLayout>
      <StyledSider
        collapsible
        collapsed={sidebarCollapsed}
        width={sidebarWidth}
        collapsedWidth={64}
        trigger={null}
        theme="light"
      >
        <Sidebar />
      </StyledSider>
      
      <Layout>
        <Header />
        
        <StyledContent>
          <ContentWrapper>
            <MainContent>
              <Outlet />
            </MainContent>
          </ContentWrapper>
        </StyledContent>
        
        <Footer />
      </Layout>
    </StyledLayout>
  );
};

export default MainLayout;
