import styled, { createGlobalStyle } from 'styled-components';

// 全局样式，仅作用于数据集微应用
export const GlobalStyle = createGlobalStyle`
  .dataset-app {
    /* 样式重置，避免与主应用冲突 */
    * {
      box-sizing: border-box;
    }
    
    /* CSS变量定义 */
    --dataset-primary: #1890ff;
    --dataset-success: #52c41a;
    --dataset-warning: #faad14;
    --dataset-error: #ff4d4f;
    --dataset-text: #262626;
    --dataset-text-secondary: #8c8c8c;
    --dataset-text-disabled: #bfbfbf;
    --dataset-bg: #ffffff;
    --dataset-bg-secondary: #fafafa;
    --dataset-bg-disabled: #f5f5f5;
    --dataset-border: #d9d9d9;
    --dataset-border-light: #f0f0f0;
    --dataset-border-radius: 6px;
    --dataset-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    --dataset-shadow-hover: 0 4px 12px rgba(0, 0, 0, 0.15);
    --dataset-shadow-modal: 0 4px 12px rgba(0, 0, 0, 0.15);
    
    /* 字体设置 */
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 
                 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
    font-size: 14px;
    line-height: 1.5715;
    color: var(--dataset-text);
    
    /* 滚动条样式 */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    
    ::-webkit-scrollbar-track {
      background: var(--dataset-bg-secondary);
      border-radius: 4px;
    }
    
    ::-webkit-scrollbar-thumb {
      background: var(--dataset-border);
      border-radius: 4px;
      
      &:hover {
        background: var(--dataset-text-secondary);
      }
    }
    
    /* 选择文本样式 */
    ::selection {
      background: rgba(24, 144, 255, 0.2);
      color: var(--dataset-text);
    }
    
    /* 焦点样式 */
    :focus-visible {
      outline: 2px solid var(--dataset-primary);
      outline-offset: 2px;
    }
  }
`;

// 应用容器样式
export const AppContainer = styled.div`
  min-height: 100vh;
  background: var(--dataset-bg-secondary);
  
  /* 确保样式隔离 */
  &.dataset-app {
    isolation: isolate;
  }
`;

// 页面容器样式
export const PageContainer = styled.div`
  padding: 24px;
  background: var(--dataset-bg-secondary);
  min-height: calc(100vh - 64px);
`;

// 内容容器样式
export const ContentContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
`;

// 卡片容器样式
export const CardContainer = styled.div`
  background: var(--dataset-bg);
  border: 1px solid var(--dataset-border);
  border-radius: var(--dataset-border-radius);
  box-shadow: var(--dataset-shadow);
  padding: 24px;
  margin-bottom: 16px;
  
  &:hover {
    box-shadow: var(--dataset-shadow-hover);
  }
`;

// 分割线样式
export const Divider = styled.div`
  height: 1px;
  background: var(--dataset-border-light);
  margin: 16px 0;
`;

// 加载容器样式
export const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  background: var(--dataset-bg);
`;

// 空状态容器样式
export const EmptyContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  padding: 48px 24px;
  text-align: center;
  background: var(--dataset-bg);
  
  .empty-icon {
    font-size: 64px;
    color: var(--dataset-text-disabled);
    margin-bottom: 16px;
  }
  
  .empty-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--dataset-text);
    margin-bottom: 8px;
  }
  
  .empty-description {
    font-size: 14px;
    color: var(--dataset-text-secondary);
    margin-bottom: 24px;
    max-width: 400px;
  }
`;

// 错误容器样式
export const ErrorContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  padding: 48px 24px;
  text-align: center;
  background: var(--dataset-bg);
  border: 1px solid var(--dataset-error);
  border-radius: var(--dataset-border-radius);
  
  .error-icon {
    font-size: 64px;
    color: var(--dataset-error);
    margin-bottom: 16px;
  }
  
  .error-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--dataset-error);
    margin-bottom: 8px;
  }
  
  .error-message {
    font-size: 14px;
    color: var(--dataset-text-secondary);
    margin-bottom: 24px;
    max-width: 400px;
    word-break: break-word;
  }
`;

// 工具栏样式
export const Toolbar = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  background: var(--dataset-bg);
  border-bottom: 1px solid var(--dataset-border);
  
  .toolbar-left {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .toolbar-right {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .toolbar-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--dataset-text);
    margin: 0;
  }
`;

// 侧边栏样式
export const Sidebar = styled.div<{ collapsed?: boolean }>`
  width: ${props => props.collapsed ? '64px' : '280px'};
  min-width: ${props => props.collapsed ? '64px' : '280px'};
  height: 100vh;
  background: var(--dataset-bg);
  border-right: 1px solid var(--dataset-border);
  transition: width 0.3s ease;
  overflow: hidden;
`;

// 主内容区域样式
export const MainContent = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
`;

// 响应式网格样式
export const ResponsiveGrid = styled.div<{ columns?: number; gap?: string }>`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: ${props => props.gap || '16px'};
  
  @media (min-width: 768px) {
    grid-template-columns: repeat(${props => props.columns || 2}, 1fr);
  }
  
  @media (min-width: 1200px) {
    grid-template-columns: repeat(${props => Math.min(props.columns || 3, 4)}, 1fr);
  }
`;
