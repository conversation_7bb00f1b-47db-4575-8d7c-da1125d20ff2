import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import BoldOutlinedSvg from "@ant-design/icons-svg/es/asn/BoldOutlined";
import AntdIcon from "../components/AntdIcon";
var BoldOutlined = function BoldOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: BoldOutlinedSvg
  }));
};

/**![bold](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTY5Ny44IDQ4MS40YzMzLjYtMzUgNTQuMi04Mi4zIDU0LjItMTM0LjN2LTEwLjJDNzUyIDIyOS4zIDY2My45IDE0MiA1NTUuMyAxNDJIMjU5LjRjLTE1LjEgMC0yNy40IDEyLjMtMjcuNCAyNy40djY3OS4xYzAgMTYuMyAxMy4yIDI5LjUgMjkuNSAyOS41aDMxOC43YzExNyAwIDIxMS44LTk0LjIgMjExLjgtMjEwLjV2LTExYzAtNzMtMzcuNC0xMzcuMy05NC4yLTE3NS4xek0zMjggMjM4aDIyNC43YzU3LjEgMCAxMDMuMyA0NC40IDEwMy4zIDk5LjN2OS41YzAgNTQuOC00Ni4zIDk5LjMtMTAzLjMgOTkuM0gzMjhWMjM4em0zNjYuNiA0MjkuNGMwIDYyLjktNTEuNyAxMTMuOS0xMTUuNSAxMTMuOUgzMjhWNTQyLjdoMjUxLjFjNjMuOCAwIDExNS41IDUxIDExNS41IDExMy45djEwLjh6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(BoldOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'BoldOutlined';
}
export default RefIcon;