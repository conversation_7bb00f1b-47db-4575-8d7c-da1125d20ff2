import { Collection, CollectionExecutionResult, CollectionTemplate } from '@/types/collection';

// 模拟Collection数据
export const mockCollections: Collection[] = [
  {
    id: 'collection_1',
    name: '用户管理流程',
    description: '包含用户的增删改查操作，以及相关的数据处理和验证',
    applicationId: 'app_1',
    actions: ['action_1', 'action_2', 'action_4'],
    variables: [
      {
        key: 'baseUrl',
        value: 'https://api.example.com',
        type: 'string',
        description: 'API基础URL'
      },
      {
        key: 'pageSize',
        value: '20',
        type: 'number',
        description: '分页大小'
      },
      {
        key: 'defaultRole',
        value: 'user',
        type: 'string',
        description: '默认用户角色'
      }
    ],
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-20T16:30:00Z',
    userPermissions: ['read', 'execute', 'write']
  },
  {
    id: 'collection_2',
    name: '订单数据分析',
    description: '订单统计查询和数据分析相关的操作集合',
    applicationId: 'app_1',
    actions: ['action_3'],
    variables: [
      {
        key: 'startDate',
        value: '2024-01-01',
        type: 'string',
        description: '开始日期'
      },
      {
        key: 'endDate',
        value: '2024-01-31',
        type: 'string',
        description: '结束日期'
      },
      {
        key: 'currency',
        value: 'CNY',
        type: 'string',
        description: '货币单位'
      }
    ],
    createdAt: '2024-01-16T09:15:00Z',
    updatedAt: '2024-01-22T14:20:00Z',
    userPermissions: ['read', 'execute']
  },
  {
    id: 'collection_3',
    name: '产品信息管理',
    description: '产品信息的查询、更新和管理操作',
    applicationId: 'app_1',
    actions: ['action_5'],
    variables: [
      {
        key: 'defaultCategory',
        value: 'electronics',
        type: 'string',
        description: '默认产品分类'
      },
      {
        key: 'maxPrice',
        value: '10000',
        type: 'number',
        description: '最大价格限制'
      },
      {
        key: 'includeOutOfStock',
        value: 'false',
        type: 'boolean',
        description: '是否包含缺货商品'
      }
    ],
    createdAt: '2024-01-18T11:30:00Z',
    updatedAt: '2024-01-23T13:45:00Z',
    userPermissions: ['read', 'execute', 'write']
  }
];

// 模拟Collection执行结果
export const mockCollectionResults: Record<string, CollectionExecutionResult> = {
  collection_1: {
    collectionId: 'collection_1',
    results: {
      action_1: {
        isExecutionSuccess: true,
        body: {
          data: [
            { id: 1, name: '张三', email: '<EMAIL>', role: 'admin' },
            { id: 2, name: '李四', email: '<EMAIL>', role: 'user' }
          ]
        },
        headers: { 'content-type': 'application/json' },
        statusCode: 200,
        isLoading: false,
        dataTypes: ['object'],
        duration: '245ms',
        size: '1.2KB',
        responseDisplayFormat: 'JSON'
      },
      action_2: {
        isExecutionSuccess: true,
        body: {
          data: {
            id: 4,
            name: '新用户',
            email: '<EMAIL>',
            role: 'user',
            createdAt: '2024-01-24T10:30:00Z'
          }
        },
        headers: { 'content-type': 'application/json' },
        statusCode: 201,
        isLoading: false,
        dataTypes: ['object'],
        duration: '380ms',
        size: '256B',
        responseDisplayFormat: 'JSON'
      },
      action_4: {
        isExecutionSuccess: true,
        body: [
          {
            id: 1,
            fullName: '张三',
            isActive: true,
            avatar: '/avatars/zhangsan.jpg',
            displayRole: 'Admin'
          },
          {
            id: 2,
            fullName: '李四',
            isActive: true,
            avatar: '/default-avatar.png',
            displayRole: 'User'
          }
        ],
        headers: {},
        isLoading: false,
        dataTypes: ['array'],
        duration: '125ms',
        size: '512B',
        responseDisplayFormat: 'JSON'
      }
    },
    variables: {
      baseUrl: 'https://api.example.com',
      pageSize: 20,
      defaultRole: 'user'
    },
    isSuccess: true,
    duration: '750ms',
    executedAt: '2024-01-24T10:30:00Z',
    errors: []
  }
};

// 模拟Collection模板
export const mockCollectionTemplates: CollectionTemplate[] = [
  {
    id: 'template_1',
    name: 'CRUD操作模板',
    description: '标准的增删改查操作模板，适用于大多数数据管理场景',
    category: '数据管理',
    actions: [
      {
        name: '获取列表',
        pluginType: 'API' as any,
        actionType: 'REST_API' as any,
        actionConfiguration: {
          httpMethod: 'GET' as any,
          path: '/api/{{resourceName}}',
          queryParameters: [
            { key: 'page', value: '{{pageNo}}', type: 'number' },
            { key: 'limit', value: '{{pageSize}}', type: 'number' }
          ]
        }
      },
      {
        name: '创建记录',
        pluginType: 'API' as any,
        actionType: 'REST_API' as any,
        actionConfiguration: {
          httpMethod: 'POST' as any,
          path: '/api/{{resourceName}}',
          body: '{{formData}}'
        }
      },
      {
        name: '更新记录',
        pluginType: 'API' as any,
        actionType: 'REST_API' as any,
        actionConfiguration: {
          httpMethod: 'PUT' as any,
          path: '/api/{{resourceName}}/{{recordId}}',
          body: '{{formData}}'
        }
      },
      {
        name: '删除记录',
        pluginType: 'API' as any,
        actionType: 'REST_API' as any,
        actionConfiguration: {
          httpMethod: 'DELETE' as any,
          path: '/api/{{resourceName}}/{{recordId}}'
        }
      }
    ],
    variables: [
      {
        key: 'resourceName',
        type: 'string',
        label: '资源名称',
        description: 'API资源的名称，如users、products等',
        defaultValue: 'users',
        required: true
      },
      {
        key: 'pageSize',
        type: 'number',
        label: '分页大小',
        description: '每页显示的记录数',
        defaultValue: 20,
        required: false
      }
    ],
    tags: ['CRUD', 'REST API', '数据管理'],
    isPublic: true,
    usageCount: 156,
    rating: 4.8,
    createdBy: 'system',
    createdAt: '2024-01-01T00:00:00Z'
  },
  {
    id: 'template_2',
    name: '数据分析模板',
    description: '包含常用的数据统计和分析查询，适用于报表和仪表板',
    category: '数据分析',
    actions: [
      {
        name: '统计查询',
        pluginType: 'DB' as any,
        actionType: 'SQL' as any,
        actionConfiguration: {
          query: `SELECT 
  DATE({{dateColumn}}) as date,
  COUNT(*) as count,
  SUM({{valueColumn}}) as total,
  AVG({{valueColumn}}) as average
FROM {{tableName}}
WHERE {{dateColumn}} >= '{{startDate}}'
  AND {{dateColumn}} <= '{{endDate}}'
GROUP BY DATE({{dateColumn}})
ORDER BY date DESC;`
        }
      },
      {
        name: '趋势分析',
        pluginType: 'DB' as any,
        actionType: 'SQL' as any,
        actionConfiguration: {
          query: `SELECT 
  {{groupColumn}},
  COUNT(*) as count,
  ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
FROM {{tableName}}
WHERE {{dateColumn}} >= '{{startDate}}'
  AND {{dateColumn}} <= '{{endDate}}'
GROUP BY {{groupColumn}}
ORDER BY count DESC;`
        }
      }
    ],
    variables: [
      {
        key: 'tableName',
        type: 'string',
        label: '表名',
        description: '要分析的数据表名称',
        required: true
      },
      {
        key: 'dateColumn',
        type: 'string',
        label: '日期字段',
        description: '用于时间筛选的日期字段名',
        defaultValue: 'created_at',
        required: true
      },
      {
        key: 'valueColumn',
        type: 'string',
        label: '数值字段',
        description: '用于统计的数值字段名',
        required: true
      },
      {
        key: 'groupColumn',
        type: 'string',
        label: '分组字段',
        description: '用于分组统计的字段名',
        required: true
      },
      {
        key: 'startDate',
        type: 'string',
        label: '开始日期',
        description: '统计的开始日期',
        required: true
      },
      {
        key: 'endDate',
        type: 'string',
        label: '结束日期',
        description: '统计的结束日期',
        required: true
      }
    ],
    tags: ['数据分析', 'SQL', '统计', '报表'],
    isPublic: true,
    usageCount: 89,
    rating: 4.6,
    createdBy: 'system',
    createdAt: '2024-01-01T00:00:00Z'
  }
];
