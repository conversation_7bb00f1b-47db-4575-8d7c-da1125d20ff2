import { createSlice, PayloadAction } from '@reduxjs/toolkit';

import { NotificationItem } from '@/types/common';

// UI状态接口
interface UIState {
  // 侧边栏状态
  sidebarCollapsed: boolean;
  sidebarWidth: number;
  
  // 活动标签页
  activeTab: string;
  openTabs: Array<{
    key: string;
    title: string;
    type: 'action' | 'collection' | 'query-builder';
    data?: any;
    modified?: boolean;
  }>;
  
  // 模态框状态
  modals: {
    createAction: boolean;
    createCollection: boolean;
    actionSettings: boolean;
    collectionSettings: boolean;
    confirmDelete: boolean;
    importData: boolean;
    exportData: boolean;
  };
  
  // 抽屉状态
  drawers: {
    actionDetails: boolean;
    collectionDetails: boolean;
    executionHistory: boolean;
    help: boolean;
  };
  
  // 通知系统
  notifications: NotificationItem[];
  
  // 加载状态
  globalLoading: boolean;
  loadingMessage: string;
  
  // 主题设置
  theme: {
    mode: 'light' | 'dark';
    primaryColor: string;
    compactMode: boolean;
  };
  
  // 布局设置
  layout: {
    showMinimap: boolean;
    showLineNumbers: boolean;
    wordWrap: boolean;
    fontSize: number;
    tabSize: number;
  };
  
  // 搜索状态
  search: {
    query: string;
    isVisible: boolean;
    results: Array<{
      id: string;
      type: 'action' | 'collection';
      title: string;
      description?: string;
    }>;
  };
  
  // 快捷键状态
  shortcuts: {
    enabled: boolean;
    customShortcuts: Record<string, string>;
  };
  
  // 面板大小
  panelSizes: {
    leftPanel: number;
    rightPanel: number;
    bottomPanel: number;
  };
  
  // 最近访问
  recentItems: Array<{
    id: string;
    type: 'action' | 'collection';
    name: string;
    accessedAt: string;
  }>;
}

const initialState: UIState = {
  sidebarCollapsed: false,
  sidebarWidth: 280,
  activeTab: 'actions',
  openTabs: [],
  modals: {
    createAction: false,
    createCollection: false,
    actionSettings: false,
    collectionSettings: false,
    confirmDelete: false,
    importData: false,
    exportData: false,
  },
  drawers: {
    actionDetails: false,
    collectionDetails: false,
    executionHistory: false,
    help: false,
  },
  notifications: [],
  globalLoading: false,
  loadingMessage: '',
  theme: {
    mode: 'light',
    primaryColor: '#1890ff',
    compactMode: false,
  },
  layout: {
    showMinimap: false,
    showLineNumbers: true,
    wordWrap: true,
    fontSize: 14,
    tabSize: 2,
  },
  search: {
    query: '',
    isVisible: false,
    results: [],
  },
  shortcuts: {
    enabled: true,
    customShortcuts: {},
  },
  panelSizes: {
    leftPanel: 280,
    rightPanel: 320,
    bottomPanel: 200,
  },
  recentItems: [],
};

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    // 侧边栏控制
    toggleSidebar: (state) => {
      state.sidebarCollapsed = !state.sidebarCollapsed;
    },
    
    setSidebarCollapsed: (state, action: PayloadAction<boolean>) => {
      state.sidebarCollapsed = action.payload;
    },
    
    setSidebarWidth: (state, action: PayloadAction<number>) => {
      state.sidebarWidth = action.payload;
    },
    
    // 标签页管理
    setActiveTab: (state, action: PayloadAction<string>) => {
      state.activeTab = action.payload;
    },
    
    openTab: (state, action: PayloadAction<{
      key: string;
      title: string;
      type: 'action' | 'collection' | 'query-builder';
      data?: any;
    }>) => {
      const existingTab = state.openTabs.find(tab => tab.key === action.payload.key);
      if (!existingTab) {
        state.openTabs.push({ ...action.payload, modified: false });
      }
      state.activeTab = action.payload.key;
    },
    
    closeTab: (state, action: PayloadAction<string>) => {
      state.openTabs = state.openTabs.filter(tab => tab.key !== action.payload);
      if (state.activeTab === action.payload && state.openTabs.length > 0) {
        state.activeTab = state.openTabs[state.openTabs.length - 1].key;
      }
    },
    
    updateTab: (state, action: PayloadAction<{
      key: string;
      updates: Partial<UIState['openTabs'][0]>;
    }>) => {
      const tab = state.openTabs.find(tab => tab.key === action.payload.key);
      if (tab) {
        Object.assign(tab, action.payload.updates);
      }
    },
    
    closeAllTabs: (state) => {
      state.openTabs = [];
      state.activeTab = 'actions';
    },
    
    // 模态框控制
    openModal: (state, action: PayloadAction<keyof UIState['modals']>) => {
      state.modals[action.payload] = true;
    },
    
    closeModal: (state, action: PayloadAction<keyof UIState['modals']>) => {
      state.modals[action.payload] = false;
    },
    
    closeAllModals: (state) => {
      Object.keys(state.modals).forEach(key => {
        state.modals[key as keyof UIState['modals']] = false;
      });
    },
    
    // 抽屉控制
    openDrawer: (state, action: PayloadAction<keyof UIState['drawers']>) => {
      state.drawers[action.payload] = true;
    },
    
    closeDrawer: (state, action: PayloadAction<keyof UIState['drawers']>) => {
      state.drawers[action.payload] = false;
    },
    
    toggleDrawer: (state, action: PayloadAction<keyof UIState['drawers']>) => {
      state.drawers[action.payload] = !state.drawers[action.payload];
    },
    
    // 通知管理
    addNotification: (state, action: PayloadAction<Omit<NotificationItem, 'id' | 'timestamp'>>) => {
      const notification: NotificationItem = {
        ...action.payload,
        id: `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date().toISOString(),
      };
      state.notifications.push(notification);
    },
    
    removeNotification: (state, action: PayloadAction<string>) => {
      state.notifications = state.notifications.filter(n => n.id !== action.payload);
    },
    
    clearNotifications: (state) => {
      state.notifications = [];
    },
    
    // 全局加载状态
    setGlobalLoading: (state, action: PayloadAction<{ loading: boolean; message?: string }>) => {
      state.globalLoading = action.payload.loading;
      state.loadingMessage = action.payload.message || '';
    },
    
    // 主题设置
    setTheme: (state, action: PayloadAction<Partial<UIState['theme']>>) => {
      state.theme = { ...state.theme, ...action.payload };
    },
    
    toggleThemeMode: (state) => {
      state.theme.mode = state.theme.mode === 'light' ? 'dark' : 'light';
    },
    
    // 布局设置
    setLayout: (state, action: PayloadAction<Partial<UIState['layout']>>) => {
      state.layout = { ...state.layout, ...action.payload };
    },
    
    // 搜索控制
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.search.query = action.payload;
    },
    
    setSearchVisible: (state, action: PayloadAction<boolean>) => {
      state.search.isVisible = action.payload;
    },
    
    setSearchResults: (state, action: PayloadAction<UIState['search']['results']>) => {
      state.search.results = action.payload;
    },
    
    clearSearch: (state) => {
      state.search.query = '';
      state.search.results = [];
    },
    
    // 快捷键设置
    setShortcuts: (state, action: PayloadAction<Partial<UIState['shortcuts']>>) => {
      state.shortcuts = { ...state.shortcuts, ...action.payload };
    },
    
    // 面板大小
    setPanelSize: (state, action: PayloadAction<{
      panel: keyof UIState['panelSizes'];
      size: number;
    }>) => {
      state.panelSizes[action.payload.panel] = action.payload.size;
    },
    
    // 最近访问
    addRecentItem: (state, action: PayloadAction<{
      id: string;
      type: 'action' | 'collection';
      name: string;
    }>) => {
      const existingIndex = state.recentItems.findIndex(item => 
        item.id === action.payload.id && item.type === action.payload.type
      );
      
      const newItem = {
        ...action.payload,
        accessedAt: new Date().toISOString(),
      };
      
      if (existingIndex > -1) {
        state.recentItems.splice(existingIndex, 1);
      }
      
      state.recentItems.unshift(newItem);
      
      // 限制最近访问项目数量
      if (state.recentItems.length > 20) {
        state.recentItems = state.recentItems.slice(0, 20);
      }
    },
    
    clearRecentItems: (state) => {
      state.recentItems = [];
    },
    
    // 重置UI状态
    resetUI: (state) => {
      return { ...initialState, theme: state.theme, layout: state.layout };
    },
  },
});

export const {
  toggleSidebar,
  setSidebarCollapsed,
  setSidebarWidth,
  setActiveTab,
  openTab,
  closeTab,
  updateTab,
  closeAllTabs,
  openModal,
  closeModal,
  closeAllModals,
  openDrawer,
  closeDrawer,
  toggleDrawer,
  addNotification,
  removeNotification,
  clearNotifications,
  setGlobalLoading,
  setTheme,
  toggleThemeMode,
  setLayout,
  setSearchQuery,
  setSearchVisible,
  setSearchResults,
  clearSearch,
  setShortcuts,
  setPanelSize,
  addRecentItem,
  clearRecentItems,
  resetUI,
} = uiSlice.actions;

export { uiSlice };
