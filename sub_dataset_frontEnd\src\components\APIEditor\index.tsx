import React from 'react';
import { Card, Typography, Button } from 'antd';
import { ApiOutlined } from '@ant-design/icons';
import styled from 'styled-components';

const { Title, Paragraph } = Typography;

const EditorContainer = styled(Card)`
  text-align: center;
  max-width: 600px;
  margin: 60px auto;
  
  .editor-icon {
    font-size: 64px;
    color: var(--dataset-primary);
    margin-bottom: 24px;
  }
  
  .editor-title {
    margin-bottom: 16px;
  }
  
  .editor-description {
    color: var(--dataset-text-secondary);
    margin-bottom: 32px;
  }
`;

interface APIEditorProps {
  actionId?: string;
  onSave?: (data: any) => void;
  onCancel?: () => void;
}

const APIEditor: React.FC<APIEditorProps> = ({ actionId, onSave, onCancel }) => {
  return (
    <EditorContainer>
      <ApiOutlined className="editor-icon" />
      <Title level={2} className="editor-title">
        API编辑器
      </Title>
      <Paragraph className="editor-description">
        API编辑器组件正在开发中，敬请期待！
        <br />
        您将能够可视化地创建和编辑REST API、GraphQL接口配置。
      </Paragraph>
      <Button type="primary" disabled>
        开始编辑（即将推出）
      </Button>
    </EditorContainer>
  );
};

export default APIEditor;
