import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, Typography, Button, Result } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';

import { PageContainer } from '@/styles/global';

const { Title } = Typography;

const ActionDetailPage: React.FC = () => {
  const { actionId } = useParams<{ actionId: string }>();
  const navigate = useNavigate();

  return (
    <PageContainer>
      <div style={{ marginBottom: 24 }}>
        <Button 
          icon={<ArrowLeftOutlined />} 
          onClick={() => navigate('/actions')}
        >
          返回Actions列表
        </Button>
      </div>
      
      <Result
        title={`Action详情页面 - ${actionId}`}
        subTitle="Action详情编辑器正在开发中，敬请期待！"
        extra={
          <Button type="primary" onClick={() => navigate('/actions')}>
            返回列表
          </Button>
        }
      />
    </PageContainer>
  );
};

export default ActionDetailPage;
