import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![flag](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAzMDVINjI0VjE5MmMwLTE3LjctMTQuMy0zMi0zMi0zMkgxODR2LTQwYzAtNC40LTMuNi04LTgtOGgtNTZjLTQuNCAwLTggMy42LTggOHY3ODRjMCA0LjQgMy42IDggOCA4aDU2YzQuNCAwIDgtMy42IDgtOFY2NDBoMjQ4djExM2MwIDE3LjcgMTQuMyAzMiAzMiAzMmg0MTZjMTcuNyAwIDMyLTE0LjMgMzItMzJWMzM3YzAtMTcuNy0xNC4zLTMyLTMyLTMyek0xODQgNTY4VjIzMmgzNjh2MzM2SDE4NHptNjU2IDE0NUg1MDR2LTczaDExMmM0LjQgMCA4LTMuNiA4LThWMzc3aDIxNnYzMzZ6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
