import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import BookFilledSvg from "@ant-design/icons-svg/es/asn/BookFilled";
import AntdIcon from "../components/AntdIcon";
var BookFilled = function BookFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: BookFilledSvg
  }));
};

/**![book](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgzMiA2NEgxOTJjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjgzMmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg2NDBjMTcuNyAwIDMyLTE0LjMgMzItMzJWOTZjMC0xNy43LTE0LjMtMzItMzItMzJ6TTY2OCAzNDUuOUw2MjEuNSAzMTIgNTcyIDM0Ny40VjEyNGg5NnYyMjEuOXoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(BookFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'BookFilled';
}
export default RefIcon;