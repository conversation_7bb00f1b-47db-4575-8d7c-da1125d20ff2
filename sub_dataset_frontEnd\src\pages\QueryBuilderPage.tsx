import React from 'react';
import { Card, Typography, Button } from 'antd';
import { DatabaseOutlined, PlusOutlined } from '@ant-design/icons';
import styled from 'styled-components';

import { PageContainer } from '@/styles/global';

const { Title, Paragraph } = Typography;

const ComingSoonCard = styled(Card)`
  text-align: center;
  max-width: 600px;
  margin: 60px auto;
  
  .coming-soon-icon {
    font-size: 64px;
    color: var(--dataset-primary);
    margin-bottom: 24px;
  }
  
  .coming-soon-title {
    margin-bottom: 16px;
  }
  
  .coming-soon-description {
    color: var(--dataset-text-secondary);
    margin-bottom: 32px;
  }
`;

const QueryBuilderPage: React.FC = () => {
  return (
    <PageContainer>
      <ComingSoonCard>
        <DatabaseOutlined className="coming-soon-icon" />
        <Title level={2} className="coming-soon-title">
          查询构建器
        </Title>
        <Paragraph className="coming-soon-description">
          可视化查询构建器正在开发中，敬请期待！
          <br />
          您将能够通过拖拽的方式构建复杂的数据库查询，支持SQL和NoSQL数据库。
        </Paragraph>
        <Button type="primary" icon={<PlusOutlined />} disabled>
          创建查询（即将推出）
        </Button>
      </ComingSoonCard>
    </PageContainer>
  );
};

export default QueryBuilderPage;
