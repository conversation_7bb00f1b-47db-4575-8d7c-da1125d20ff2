import React from 'react';
import { Spin, SpinProps } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';
import styled, { keyframes } from 'styled-components';

// 动画定义
const spin = keyframes`
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
`;

const pulse = keyframes`
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
`;

const bounce = keyframes`
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
`;

// 样式组件
const SpinnerContainer = styled.div<{ 
  fullScreen?: boolean; 
  overlay?: boolean;
  size?: 'small' | 'default' | 'large';
}>`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  
  ${props => props.fullScreen && `
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
  `}
  
  ${props => props.overlay && `
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(2px);
  `}
  
  min-height: ${props => {
    switch (props.size) {
      case 'small': return '60px';
      case 'large': return '200px';
      default: return '120px';
    }
  }};
  
  padding: 24px;
`;

const CustomSpinner = styled.div<{ size?: 'small' | 'default' | 'large' }>`
  width: ${props => {
    switch (props.size) {
      case 'small': return '20px';
      case 'large': return '48px';
      default: return '32px';
    }
  }};
  height: ${props => {
    switch (props.size) {
      case 'small': return '20px';
      case 'large': return '48px';
      default: return '32px';
    }
  }};
  border: 3px solid var(--dataset-border-light);
  border-top: 3px solid var(--dataset-primary);
  border-radius: 50%;
  animation: ${spin} 1s linear infinite;
`;

const DotsSpinner = styled.div<{ size?: 'small' | 'default' | 'large' }>`
  display: flex;
  gap: ${props => {
    switch (props.size) {
      case 'small': return '4px';
      case 'large': return '8px';
      default: return '6px';
    }
  }};
  
  .dot {
    width: ${props => {
      switch (props.size) {
        case 'small': return '6px';
        case 'large': return '12px';
        default: return '8px';
      }
    }};
    height: ${props => {
      switch (props.size) {
        case 'small': return '6px';
        case 'large': return '12px';
        default: return '8px';
      }
    }};
    background: var(--dataset-primary);
    border-radius: 50%;
    animation: ${bounce} 1.4s ease-in-out infinite both;
    
    &:nth-child(1) { animation-delay: -0.32s; }
    &:nth-child(2) { animation-delay: -0.16s; }
    &:nth-child(3) { animation-delay: 0s; }
  }
`;

const PulseSpinner = styled.div<{ size?: 'small' | 'default' | 'large' }>`
  width: ${props => {
    switch (props.size) {
      case 'small': return '20px';
      case 'large': return '48px';
      default: return '32px';
    }
  }};
  height: ${props => {
    switch (props.size) {
      case 'small': return '20px';
      case 'large': return '48px';
      default: return '32px';
    }
  }};
  background: var(--dataset-primary);
  border-radius: 50%;
  animation: ${pulse} 1.5s ease-in-out infinite;
`;

const LoadingText = styled.div<{ size?: 'small' | 'default' | 'large' }>`
  margin-top: 16px;
  color: var(--dataset-text-secondary);
  font-size: ${props => {
    switch (props.size) {
      case 'small': return '12px';
      case 'large': return '16px';
      default: return '14px';
    }
  }};
  text-align: center;
  max-width: 300px;
`;

// 加载组件属性接口
interface LoadingSpinnerProps extends Omit<SpinProps, 'indicator'> {
  type?: 'default' | 'custom' | 'dots' | 'pulse';
  fullScreen?: boolean;
  overlay?: boolean;
  tip?: string;
  description?: string;
}

// 加载组件
const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  type = 'default',
  size = 'default',
  fullScreen = false,
  overlay = false,
  tip,
  description,
  spinning = true,
  children,
  ...props
}) => {
  // 渲染不同类型的加载指示器
  const renderSpinner = () => {
    switch (type) {
      case 'custom':
        return <CustomSpinner size={size} />;
      case 'dots':
        return (
          <DotsSpinner size={size}>
            <div className="dot" />
            <div className="dot" />
            <div className="dot" />
          </DotsSpinner>
        );
      case 'pulse':
        return <PulseSpinner size={size} />;
      default:
        return (
          <Spin
            size={size}
            indicator={<LoadingOutlined style={{ fontSize: size === 'large' ? 48 : size === 'small' ? 20 : 32 }} />}
            {...props}
          />
        );
    }
  };

  // 如果有children且不是spinning状态，直接返回children
  if (children && !spinning) {
    return <>{children}</>;
  }

  // 如果有children且是spinning状态，使用Ant Design的Spin包装
  if (children && spinning) {
    return (
      <Spin
        spinning={spinning}
        size={size}
        tip={tip}
        indicator={type !== 'default' ? renderSpinner() : undefined}
        {...props}
      >
        {children}
      </Spin>
    );
  }

  // 独立的加载组件
  return (
    <SpinnerContainer 
      fullScreen={fullScreen} 
      overlay={overlay} 
      size={size}
    >
      {renderSpinner()}
      {(tip || description) && (
        <LoadingText size={size}>
          {tip && <div style={{ fontWeight: 500 }}>{tip}</div>}
          {description && <div style={{ marginTop: 4, opacity: 0.8 }}>{description}</div>}
        </LoadingText>
      )}
    </SpinnerContainer>
  );
};

// 全屏加载组件
export const FullScreenLoading: React.FC<{
  tip?: string;
  description?: string;
  type?: LoadingSpinnerProps['type'];
}> = ({ tip = '正在加载...', description, type = 'default' }) => (
  <LoadingSpinner
    type={type}
    size="large"
    fullScreen
    overlay
    tip={tip}
    description={description}
  />
);

// 页面加载组件
export const PageLoading: React.FC<{
  tip?: string;
  description?: string;
}> = ({ tip = '正在加载页面...', description }) => (
  <div style={{ 
    minHeight: '400px', 
    display: 'flex', 
    alignItems: 'center', 
    justifyContent: 'center' 
  }}>
    <LoadingSpinner
      size="large"
      tip={tip}
      description={description}
    />
  </div>
);

// 内联加载组件
export const InlineLoading: React.FC<{
  tip?: string;
  size?: 'small' | 'default';
}> = ({ tip, size = 'small' }) => (
  <LoadingSpinner
    size={size}
    tip={tip}
    style={{ display: 'inline-flex', alignItems: 'center', gap: '8px' }}
  />
);

// 按钮加载组件
export const ButtonLoading: React.FC = () => (
  <LoadingSpinner
    type="custom"
    size="small"
    style={{ margin: 0 }}
  />
);

export default LoadingSpinner;
