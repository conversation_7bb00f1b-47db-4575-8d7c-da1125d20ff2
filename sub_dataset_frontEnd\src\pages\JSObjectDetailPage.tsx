import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, Typography, Button, Result } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';

import { PageContainer } from '@/styles/global';

const { Title } = Typography;

const JSObjectDetailPage: React.FC = () => {
  const { jsObjectId } = useParams<{ jsObjectId: string }>();
  const navigate = useNavigate();

  return (
    <PageContainer>
      <div style={{ marginBottom: 24 }}>
        <Button 
          icon={<ArrowLeftOutlined />} 
          onClick={() => navigate('/js-objects')}
        >
          返回JS对象列表
        </Button>
      </div>
      
      <Result
        title={`JS对象详情页面 - ${jsObjectId}`}
        subTitle="JS对象编辑器正在开发中，敬请期待！"
        extra={
          <Button type="primary" onClick={() => navigate('/js-objects')}>
            返回列表
          </Button>
        }
      />
    </PageContainer>
  );
};

export default JSObjectDetailPage;
