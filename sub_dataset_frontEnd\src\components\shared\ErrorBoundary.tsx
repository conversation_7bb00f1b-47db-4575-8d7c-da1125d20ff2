import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON>, Result } from 'antd';
import { ReloadOutlined, BugOutlined } from '@ant-design/icons';
import styled from 'styled-components';

// 样式组件
const ErrorContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--dataset-bg-secondary);
  padding: 24px;
`;

const ErrorDetails = styled.details`
  margin-top: 16px;
  padding: 16px;
  background: var(--dataset-bg);
  border: 1px solid var(--dataset-border);
  border-radius: var(--dataset-border-radius);
  
  summary {
    cursor: pointer;
    font-weight: 600;
    color: var(--dataset-text);
    margin-bottom: 8px;
    
    &:hover {
      color: var(--dataset-primary);
    }
  }
  
  pre {
    background: var(--dataset-bg-secondary);
    padding: 12px;
    border-radius: var(--dataset-border-radius);
    overflow-x: auto;
    font-size: 12px;
    color: var(--dataset-text-secondary);
    margin: 0;
    white-space: pre-wrap;
    word-break: break-word;
  }
`;

// 错误状态接口
interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
}

// 错误边界属性接口
interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showDetails?: boolean;
}

// 错误边界组件
class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    // 更新state以显示错误UI
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // 记录错误信息
    this.setState({
      error,
      errorInfo,
    });

    // 调用错误回调
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // 发送错误报告到监控服务
    this.reportError(error, errorInfo);
  }

  // 错误报告
  private reportError = (error: Error, errorInfo: ErrorInfo) => {
    try {
      // 这里可以集成错误监控服务，如Sentry、LogRocket等
      console.group('🚨 Error Boundary Caught Error');
      console.error('Error:', error);
      console.error('Error Info:', errorInfo);
      console.error('Component Stack:', errorInfo.componentStack);
      console.error('Error Stack:', error.stack);
      console.groupEnd();

      // 模拟发送错误报告
      const errorReport = {
        errorId: this.state.errorId,
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
      };

      // 在实际项目中，这里应该发送到错误监控服务
      console.log('Error Report:', errorReport);
    } catch (reportError) {
      console.error('Failed to report error:', reportError);
    }
  };

  // 重试处理
  private handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
    });
  };

  // 刷新页面
  private handleReload = () => {
    window.location.reload();
  };

  // 复制错误信息
  private handleCopyError = () => {
    const { error, errorInfo, errorId } = this.state;
    
    const errorText = `
错误ID: ${errorId}
时间: ${new Date().toLocaleString()}
错误信息: ${error?.message || 'Unknown error'}
错误堆栈:
${error?.stack || 'No stack trace available'}

组件堆栈:
${errorInfo?.componentStack || 'No component stack available'}

页面URL: ${window.location.href}
用户代理: ${navigator.userAgent}
    `.trim();

    navigator.clipboard.writeText(errorText).then(() => {
      console.log('错误信息已复制到剪贴板');
    }).catch(() => {
      console.error('复制错误信息失败');
    });
  };

  render() {
    const { hasError, error, errorInfo, errorId } = this.state;
    const { children, fallback, showDetails = true } = this.props;

    if (hasError) {
      // 如果提供了自定义fallback，使用它
      if (fallback) {
        return fallback;
      }

      // 默认错误UI
      return (
        <ErrorContainer>
          <div style={{ maxWidth: 600, width: '100%' }}>
            <Result
              status="error"
              title="应用程序出现错误"
              subTitle={`错误ID: ${errorId} - 抱歉，应用程序遇到了意外错误。请尝试刷新页面或联系技术支持。`}
              extra={[
                <Button
                  key="retry"
                  type="primary"
                  icon={<ReloadOutlined />}
                  onClick={this.handleRetry}
                >
                  重试
                </Button>,
                <Button
                  key="reload"
                  icon={<ReloadOutlined />}
                  onClick={this.handleReload}
                >
                  刷新页面
                </Button>,
                showDetails && (
                  <Button
                    key="copy"
                    icon={<BugOutlined />}
                    onClick={this.handleCopyError}
                  >
                    复制错误信息
                  </Button>
                ),
              ].filter(Boolean)}
            />

            {showDetails && error && (
              <ErrorDetails>
                <summary>查看错误详情</summary>
                <div>
                  <strong>错误信息:</strong>
                  <pre>{error.message}</pre>
                </div>
                {error.stack && (
                  <div>
                    <strong>错误堆栈:</strong>
                    <pre>{error.stack}</pre>
                  </div>
                )}
                {errorInfo?.componentStack && (
                  <div>
                    <strong>组件堆栈:</strong>
                    <pre>{errorInfo.componentStack}</pre>
                  </div>
                )}
              </ErrorDetails>
            )}
          </div>
        </ErrorContainer>
      );
    }

    return children;
  }
}

// 函数式错误边界Hook（用于函数组件）
export const useErrorHandler = () => {
  const [error, setError] = React.useState<Error | null>(null);

  const resetError = React.useCallback(() => {
    setError(null);
  }, []);

  const captureError = React.useCallback((error: Error) => {
    setError(error);
  }, []);

  React.useEffect(() => {
    if (error) {
      throw error;
    }
  }, [error]);

  return { captureError, resetError };
};

// 高阶组件包装器
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ErrorBoundaryProps, 'children'>
) => {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;

  return WrappedComponent;
};

export default ErrorBoundary;
