import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import DollarCircleFilledSvg from "@ant-design/icons-svg/es/asn/DollarCircleFilled";
import AntdIcon from "../components/AntdIcon";
var DollarCircleFilled = function DollarCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: DollarCircleFilledSvg
  }));
};

/**![dollar-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0yMi4zIDY2NS4ybC4yIDMxLjdjMCA0LjQtMy42IDguMS04IDguMWgtMjguNGMtNC40IDAtOC0zLjYtOC04di0zMS40QzQwMS4zIDcyMyAzNTkuNSA2NzIuNCAzNTUgNjE3LjRjLS40LTQuNyAzLjMtOC43IDgtOC43aDQ2LjJjMy45IDAgNy4zIDIuOCA3LjkgNi42IDUuMSAzMS43IDI5LjggNTUuNCA3NC4xIDYxLjNWNTMzLjlsLTI0LjctNi4zYy01Mi4zLTEyLjUtMTAyLjEtNDUuMS0xMDIuMS0xMTIuNyAwLTcyLjkgNTUuNC0xMTIuMSAxMjYuMi0xMTl2LTMzYzAtNC40IDMuNi04IDgtOGgyOC4xYzQuNCAwIDggMy42IDggOHYzMi43YzY4LjUgNi45IDExOS45IDQ2LjkgMTI1LjkgMTA5LjIuNSA0LjctMy4yIDguOC04IDguOGgtNDQuOWMtNCAwLTcuNC0zLTcuOS02LjktNC0yOS4yLTI3LjQtNTMtNjUuNS01OC4ydjEzNC4zbDI1LjQgNS45YzY0LjggMTYgMTA4LjkgNDcgMTA4LjkgMTE2LjQgMCA3NS4zLTU2IDExNy4zLTEzNC4zIDEyNC4xek00MjYuNiA0MTAuM2MwIDI1LjQgMTUuNyA0NS4xIDQ5LjUgNTcuMyA0LjcgMS45IDkuNCAzLjQgMTUgNXYtMTI0Yy0zNi45IDQuNy02NC41IDI1LjQtNjQuNSA2MS43em0xMTYuNSAxMzUuMmMtMi44LS42LTUuNi0xLjMtOC44LTIuMlY2NzdjNDIuNi0zLjggNzItMjcuMiA3Mi02Ni40IDAtMzAuNy0xNS45LTUwLjctNjMuMi02NS4xeiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(DollarCircleFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'DollarCircleFilled';
}
export default RefIcon;