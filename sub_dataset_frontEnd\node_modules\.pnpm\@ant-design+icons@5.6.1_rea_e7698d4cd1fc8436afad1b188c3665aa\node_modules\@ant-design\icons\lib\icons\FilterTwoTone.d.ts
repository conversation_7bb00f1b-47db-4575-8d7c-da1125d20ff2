import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![filter](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQyMC42IDc5OGgxODIuOVY2NDJINDIwLjZ6TTQxMSA1NjEuNGw5LjUgMTYuNmgxODNsOS41LTE2LjZMODExLjMgMjI2SDIxMi43eiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNODgwLjEgMTU0SDE0My45Yy0yNC41IDAtMzkuOCAyNi43LTI3LjUgNDhMMzQ5IDU5Ny40VjgzOGMwIDE3LjcgMTQuMiAzMiAzMS44IDMyaDI2Mi40YzE3LjYgMCAzMS44LTE0LjMgMzEuOC0zMlY1OTcuNEw5MDcuNyAyMDJjMTIuMi0yMS4zLTMuMS00OC0yNy42LTQ4ek02MDMuNSA3OThINDIwLjZWNjQyaDE4Mi45djE1NnptOS41LTIzNi42bC05LjUgMTYuNmgtMTgzbC05LjUtMTYuNkwyMTIuNyAyMjZoNTk4LjZMNjEzIDU2MS40eiIgZmlsbD0iIzE2NzdmZiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
