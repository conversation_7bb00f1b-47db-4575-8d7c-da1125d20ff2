"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _FilePptOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/FilePptOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var FilePptOutlined = function FilePptOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _FilePptOutlined.default
  }));
};

/**![file-ppt](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQyNCA0NzZjLTQuNCAwLTggMy42LTggOHYyNzZjMCA0LjQgMy42IDggOCA4aDMyLjVjNC40IDAgOC0zLjYgOC04di05NS41aDYzLjNjNTkuNCAwIDk2LjItMzguOSA5Ni4yLTk0LjEgMC01NC41LTM2LjMtOTQuMy05Ni05NC4zSDQyNHptMTUwLjYgOTQuM2MwIDQzLjQtMjYuNSA1NC4zLTcxLjIgNTQuM2gtMzguOVY1MTYuMmg1Ni4yYzMzLjggMCA1My45IDE5LjcgNTMuOSA1NC4xem0yODAtMjgxLjdMNjM5LjQgNzMuNGMtNi02LTE0LjEtOS40LTIyLjYtOS40SDE5MmMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2ODMyYzAgMTcuNyAxNC4zIDMyIDMyIDMyaDY0MGMxNy43IDAgMzItMTQuMyAzMi0zMlYzMTEuM2MwLTguNS0zLjQtMTYuNy05LjQtMjIuN3pNNzkwLjIgMzI2SDYwMlYxMzcuOEw3OTAuMiAzMjZ6bTEuOCA1NjJIMjMyVjEzNmgzMDJ2MjE2YTQyIDQyIDAgMDA0MiA0MmgyMTZ2NDk0eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(FilePptOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FilePptOutlined';
}
var _default = exports.default = RefIcon;