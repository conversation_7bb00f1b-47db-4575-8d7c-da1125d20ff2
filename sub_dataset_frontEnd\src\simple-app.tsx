import React from 'react';
import { BrowserRouter } from 'react-router-dom';
import { Provider } from 'react-redux';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';

import { store } from './store';

// 简单的测试组件
const SimpleTestApp: React.FC = () => {
  return (
    <Provider store={store}>
      <ConfigProvider locale={zhCN}>
        <BrowserRouter>
          <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
            <h1 style={{ color: '#1890ff' }}>🎉 PagePlug 数据集管理应用</h1>
            <p>应用已成功启动！</p>
            
            <div style={{ 
              background: '#f0f2f5', 
              padding: '20px', 
              borderRadius: '8px',
              marginTop: '20px'
            }}>
              <h2>✅ 核心功能模块</h2>
              <ul>
                <li>📊 API管理 - 管理REST API、GraphQL接口</li>
                <li>📁 数据集合 - 组织相关的API接口</li>
                <li>🔍 查询构建器 - 可视化查询构建</li>
                <li>💻 JS对象管理 - JavaScript函数管理</li>
                <li>📝 查询模板 - 预定义查询模板</li>
              </ul>
            </div>

            <div style={{ 
              background: '#e6f7ff', 
              padding: '20px', 
              borderRadius: '8px',
              marginTop: '20px'
            }}>
              <h2>🚀 技术栈</h2>
              <ul>
                <li>React 18 + TypeScript</li>
                <li>Redux Toolkit + React Redux</li>
                <li>Ant Design + Styled Components</li>
                <li>Webpack 5 + Module Federation</li>
                <li>TailwindCSS</li>
              </ul>
            </div>

            <div style={{ 
              background: '#f6ffed', 
              padding: '20px', 
              borderRadius: '8px',
              marginTop: '20px'
            }}>
              <h2>📝 下一步</h2>
              <p>基础架构已搭建完成，您可以：</p>
              <ol>
                <li>查看完整的应用界面（切换到完整模式）</li>
                <li>连接真实的后端API</li>
                <li>完善各个功能模块</li>
                <li>添加更多测试用例</li>
              </ol>
            </div>

            <div style={{ marginTop: '30px', textAlign: 'center' }}>
              <button 
                style={{
                  background: '#1890ff',
                  color: 'white',
                  border: 'none',
                  padding: '10px 20px',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '16px'
                }}
                onClick={() => {
                  window.location.href = '/actions';
                }}
              >
                进入完整应用 →
              </button>
            </div>
          </div>
        </BrowserRouter>
      </ConfigProvider>
    </Provider>
  );
};

export default SimpleTestApp;
