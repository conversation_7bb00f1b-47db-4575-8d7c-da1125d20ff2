import React, { useEffect } from 'react';
import { Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { Layout } from 'antd';
import styled from 'styled-components';

import { AppHeader } from './components/Layout/AppHeader';
import { AppSidebar } from './components/Layout/AppSidebar';
// import { LoadingSpinner } from './components/Common/LoadingSpinner';
import { useTheme } from './hooks/useTheme';
// import { RouteComponents } from './utils/codeSplitting';
import DatasourceList from './pages/DatasourceList';
import DatasourceCreate from './pages/DatasourceCreate';
import DatasourceDetail from './pages/DatasourceDetail';
import QueryEditor from './pages/QueryEditor';
import PluginManagement from './pages/PluginManagement';
import { initPerformanceMonitoring, performanceCollector } from './utils/performance';
import { initCodeSplitting, preloadStrategy } from './utils/codeSplitting';

const { Content } = Layout;

// 样式化组件
const AppContainer = styled.div<{ $isDark: boolean }>`
  min-height: 100vh;
  background-color: ${props => props.$isDark ? '#141414' : '#f5f5f5'};
  
  .ant-layout {
   width:1024px;
    background-color: transparent;
  }
  
  .ant-layout-content {
    padding: 24px;
    margin: 0;
    min-height: calc(100vh - 64px);
    overflow: auto;
  }

  .ant-layout-sider {
    flex: 0 0 auto !important;
  }

  .ant-layout:not(.ant-layout-has-sider) > .ant-layout {
    flex: 1;
  }
`;

const ContentWrapper = styled.div`
  width: 100%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  min-height: calc(100vh - 112px);
  padding: 24px;
`;

const App: React.FC = () => {
  const { isDark } = useTheme();
  const location = useLocation();

  // 初始化性能监控和代码分割
  useEffect(() => {
    initPerformanceMonitoring();
    initCodeSplitting();
  }, []);

  // 监听路由变化进行性能记录和预加载
  useEffect(() => {
    performanceCollector.recordMetric('route.change', performance.now());
    preloadStrategy.preloadByUserBehavior(location.pathname);
  }, [location.pathname]);

  return (
    <AppContainer $isDark={isDark} id="datasource-app">
      <Layout>
        <AppHeader />
        <Layout hasSider>
          <AppSidebar />
          <Layout>
            <Content>
              <ContentWrapper>
                <Routes>
                    {/* 默认重定向到数据源列表 */}
                    <Route path="/" element={<Navigate to="/datasources" replace />} />

                    {/* 数据源管理路由 */}
                    <Route path="/datasources" element={<DatasourceList />} />
                    <Route path="/datasources/create" element={<DatasourceCreate />} />
                    <Route path="/datasources/:id" element={<DatasourceDetail />} />
                    <Route path="/datasources/:id/edit" element={<DatasourceCreate />} />

                    {/* 查询编辑器路由 */}
                    <Route path="/query" element={<QueryEditor />} />
                    <Route path="/query/:datasourceId" element={<QueryEditor />} />

                    {/* 插件管理路由 */}
                    <Route path="/plugins" element={<PluginManagement />} />

                    {/* 404 页面 */}
                    <Route path="*" element={<Navigate to="/datasources" replace />} />
                </Routes>
              </ContentWrapper>
            </Content>
          </Layout>
        </Layout>
      </Layout>
    </AppContainer>
  );
};

export default App;
