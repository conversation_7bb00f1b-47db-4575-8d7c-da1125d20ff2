"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _FacebookFilled = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/FacebookFilled"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var FacebookFilled = function FacebookFilled(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _FacebookFilled.default
  }));
};

/**![facebook](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnptLTkyLjQgMjMzLjVoLTYzLjljLTUwLjEgMC01OS44IDIzLjgtNTkuOCA1OC44djc3LjFoMTE5LjZsLTE1LjYgMTIwLjdoLTEwNFY5MTJINTM5LjJWNjAyLjJINDM0LjlWNDgxLjRoMTA0LjN2LTg5YzAtMTAzLjMgNjMuMS0xNTkuNiAxNTUuMy0xNTkuNiA0NC4yIDAgODIuMSAzLjMgOTMuMiA0Ljh2MTA3Ljl6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(FacebookFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FacebookFilled';
}
var _default = exports.default = RefIcon;