import React, { Suspense, useEffect } from 'react';
import { Provider } from 'react-redux';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { ThemeProvider } from 'styled-components';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';

import { store } from '@/store';
import { useTheme } from '@/hooks/useTheme';
import { GlobalStyle, AppContainer, LoadingContainer } from '@/styles/global';
import { lightTheme } from '@/styles/theme';
import AppRoutes from '@/routes';
import ErrorBoundary from '@/components/shared/ErrorBoundary';
import NotificationProvider from '@/components/shared/NotificationProvider';
import LoadingSpinner from '@/components/shared/LoadingSpinner';

// 主题包装组件
const ThemeWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { theme } = useTheme();
  
  return (
    <ThemeProvider theme={theme}>
      <ConfigProvider
        locale={zhCN}
        theme={{
          token: {
            colorPrimary: theme.colors.primary,
            colorSuccess: theme.colors.success,
            colorWarning: theme.colors.warning,
            colorError: theme.colors.error,
            colorTextBase: theme.colors.text.primary,
            colorBgBase: theme.colors.background.primary,
            borderRadius: parseInt(theme.borderRadius.md),
            fontFamily: theme.typography.fontFamily,
            fontSize: parseInt(theme.typography.fontSize.base),
          },
        }}
      >
        {children}
      </ConfigProvider>
    </ThemeProvider>
  );
};

// 应用加载组件
const AppLoading: React.FC = () => (
  <LoadingContainer>
    <LoadingSpinner size="large" tip="正在加载数据集应用..." />
  </LoadingContainer>
);

// 主应用组件
const DatasetApp: React.FC = () => {
  useEffect(() => {
    // 应用初始化逻辑
    console.log('Dataset App initialized');
    
    // 设置全局错误处理
    const handleGlobalError = (event: ErrorEvent) => {
      console.error('Global error:', event.error);
    };
    
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.error('Unhandled promise rejection:', event.reason);
    };
    
    window.addEventListener('error', handleGlobalError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    
    return () => {
      window.removeEventListener('error', handleGlobalError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);

  return (
    <ErrorBoundary>
      <Provider store={store}>
        <ThemeWrapper>
          <GlobalStyle />
          <AppContainer className="dataset-app">
            <BrowserRouter basename="/dataset">
              <NotificationProvider>
                <Suspense fallback={<AppLoading />}>
                  <AppRoutes />
                </Suspense>
              </NotificationProvider>
            </BrowserRouter>
          </AppContainer>
        </ThemeWrapper>
      </Provider>
    </ErrorBoundary>
  );
};

// 用于微前端集成的包装组件
const DatasetAppWrapper: React.FC<{
  basename?: string;
  initialData?: any;
  onNavigate?: (path: string) => void;
}> = ({ basename = '/dataset', initialData, onNavigate }) => {
  return (
    <ErrorBoundary>
      <Provider store={store}>
        <ThemeWrapper>
          <GlobalStyle />
          <AppContainer className="dataset-app">
            <BrowserRouter basename={basename}>
              <NotificationProvider>
                <Suspense fallback={<AppLoading />}>
                  <AppRoutes initialData={initialData} onNavigate={onNavigate} />
                </Suspense>
              </NotificationProvider>
            </BrowserRouter>
          </AppContainer>
        </ThemeWrapper>
      </Provider>
    </ErrorBoundary>
  );
};

// 独立运行的应用组件
const StandaloneApp: React.FC = () => {
  return (
    <div style={{ height: '100vh', overflow: 'hidden' }}>
      <DatasetApp />
    </div>
  );
};

// 导出不同的组件用于不同场景
export default DatasetApp;
export { DatasetAppWrapper, StandaloneApp };

// 微前端生命周期函数
export const mount = (element: HTMLElement, props?: any) => {
  const React = require('react');
  const ReactDOM = require('react-dom/client');
  
  const root = ReactDOM.createRoot(element);
  root.render(React.createElement(DatasetAppWrapper, props));
  
  return {
    unmount: () => {
      root.unmount();
    },
    update: (newProps: any) => {
      root.render(React.createElement(DatasetAppWrapper, newProps));
    },
  };
};

export const unmount = (element: HTMLElement) => {
  // 清理逻辑
  const React = require('react');
  const ReactDOM = require('react-dom/client');
  
  // 如果有root实例，则卸载
  if (element && element._reactRootContainer) {
    ReactDOM.unmountComponentAtNode(element);
  }
};

// 微前端配置
export const microAppConfig = {
  name: 'dataset-app',
  version: '1.0.0',
  description: 'PagePlug数据集管理微前端应用',
  routes: [
    '/dataset',
    '/dataset/actions',
    '/dataset/collections',
    '/dataset/query-builder',
  ],
  dependencies: {
    react: '^18.2.0',
    'react-dom': '^18.2.0',
    antd: '^5.2.0',
    'styled-components': '^6.0.0',
  },
};
