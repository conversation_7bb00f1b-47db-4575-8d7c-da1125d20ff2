// 通用类型定义

export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  code?: string;
  timestamp: string;
}

export interface PaginatedResponse<T = any> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface ApiError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
}

export interface LoadingState {
  isLoading: boolean;
  error: string | null;
}

export interface User {
  id: string;
  username: string;
  email: string;
  name: string;
  avatar?: string;
  roles: string[];
  permissions: string[];
}

export interface Organization {
  id: string;
  name: string;
  slug: string;
  logo?: string;
  settings: Record<string, any>;
}

export interface Application {
  id: string;
  name: string;
  slug: string;
  organizationId: string;
  isPublic: boolean;
  settings: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface Page {
  id: string;
  name: string;
  slug: string;
  applicationId: string;
  isDefault: boolean;
  isHidden: boolean;
  settings: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface Theme {
  primaryColor: string;
  successColor: string;
  warningColor: string;
  errorColor: string;
  textColor: string;
  backgroundColor: string;
  borderColor: string;
  borderRadius: string;
  fontFamily: string;
  fontSize: string;
  mode: 'light' | 'dark';
}

export interface UIState {
  sidebarCollapsed: boolean;
  activeTab: string;
  selectedItems: string[];
  filters: Record<string, any>;
  sorting: {
    field: string;
    direction: 'asc' | 'desc';
  };
  pagination: {
    page: number;
    pageSize: number;
  };
}

export interface NotificationItem {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  timestamp: string;
}

export interface ContextMenuItem {
  key: string;
  label: string;
  icon?: string;
  disabled?: boolean;
  danger?: boolean;
  onClick: () => void;
}

export interface TabItem {
  key: string;
  label: string;
  content: React.ReactNode;
  closable?: boolean;
  disabled?: boolean;
}

export interface TreeNode {
  key: string;
  title: string;
  icon?: React.ReactNode;
  children?: TreeNode[];
  isLeaf?: boolean;
  disabled?: boolean;
  selectable?: boolean;
  checkable?: boolean;
  data?: any;
}

export interface FormField {
  name: string;
  label: string;
  type: 'input' | 'textarea' | 'select' | 'checkbox' | 'radio' | 'date' | 'number';
  required?: boolean;
  placeholder?: string;
  options?: { label: string; value: any }[];
  validation?: {
    pattern?: RegExp;
    min?: number;
    max?: number;
    message?: string;
  };
  dependencies?: string[];
}

export interface SearchFilters {
  query?: string;
  type?: string;
  status?: string;
  dateRange?: [string, string];
  tags?: string[];
  [key: string]: any;
}

export interface ExportOptions {
  format: 'json' | 'csv' | 'excel' | 'pdf';
  fields?: string[];
  filters?: SearchFilters;
  includeMetadata?: boolean;
}

export interface ImportOptions {
  format: 'json' | 'csv' | 'excel';
  mapping?: Record<string, string>;
  skipErrors?: boolean;
  updateExisting?: boolean;
}
