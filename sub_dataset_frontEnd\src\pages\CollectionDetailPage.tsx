import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, Typography, Button, Result } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';

import { PageContainer } from '@/styles/global';

const { Title } = Typography;

const CollectionDetailPage: React.FC = () => {
  const { collectionId } = useParams<{ collectionId: string }>();
  const navigate = useNavigate();

  return (
    <PageContainer>
      <div style={{ marginBottom: 24 }}>
        <Button 
          icon={<ArrowLeftOutlined />} 
          onClick={() => navigate('/collections')}
        >
          返回Collections列表
        </Button>
      </div>
      
      <Result
        title={`Collection详情页面 - ${collectionId}`}
        subTitle="Collection详情编辑器正在开发中，敬请期待！"
        extra={
          <Button type="primary" onClick={() => navigate('/collections')}>
            返回列表
          </Button>
        }
      />
    </PageContainer>
  );
};

export default CollectionDetailPage;
