import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import BorderlessTableOutlinedSvg from "@ant-design/icons-svg/es/asn/BorderlessTableOutlined";
import AntdIcon from "../components/AntdIcon";
var BorderlessTableOutlined = function BorderlessTableOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: BorderlessTableOutlinedSvg
  }));
};

/**![borderless-table](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik0xMTcgMzY4aDIzMXY2NEgxMTd6bTU1OSAwaDI0MXY2NEg2NzZ6bS0yNjQgMGgyMDB2NjRINDEyem0wIDIyNGgyMDB2NjRINDEyem0yNjQgMGgyNDF2NjRINjc2em0tNTU5IDBoMjMxdjY0SDExN3ptMjk1LTE2MFYxNzloLTY0djY2Nmg2NFY1OTJ6bTI2NC02NFYxNzloLTY0djY2Nmg2NFY0MzJ6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(BorderlessTableOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'BorderlessTableOutlined';
}
export default RefIcon;