import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![wechat-work](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNOTA5Ljc4IDcyOS41OWExMzUuODcgMTM1Ljg3IDAgMDAtNDcuMDQgMTkuMDQgMTE0LjI0IDExNC4yNCAwIDAxLTUxLjQgMzEuMDggNzYuMjkgNzYuMjkgMCAwMTI0LjQ1LTQ1LjQyIDE2OS4zIDE2OS4zIDAgMDAyMy40LTU1LjAyIDUwLjQxIDUwLjQxIDAgMTE1MC42IDUwLjMyem0tOTIuMjEtMTIwLjc2YTE2OC44MyAxNjguODMgMCAwMC01NC44MS0yMy42OCA1MC40MSA1MC40MSAwIDAxLTUwLjQtNTAuNDIgNTAuNDEgNTAuNDEgMCAxMTEwMC44IDAgMTM3LjUgMTM3LjUgMCAwMDE4LjgyIDQ3LjIgMTE0LjggMTE0LjggMCAwMTMwLjc2IDUxLjY2IDc2LjA4IDc2LjA4IDAgMDEtNDUuMDItMjQuNzZoLS4xOXptLTgzLjA0LTE3Ny43MWMtMTUuMTktMTI3LjMzLTE0Ni45OC0yMjcuMS0zMDYuNDQtMjI3LjEtMTY5Ljg3IDAtMzA4LjA5IDExMy4xLTMwOC4wOSAyNTIuMkEyMzUuODEgMjM1LjgxIDAgMDAyMzAuMDYgNjQ3LjZhMzExLjI4IDMxMS4yOCAwIDAwMzMuNiAyMS41OUwyNTAgNzIzLjc2YzQuOTMgMi4zMSA5LjcgNC43OCAxNC43NSA2LjlsNjktMzQuNWMxMC4wNyAyLjYxIDIwLjY4IDQuMyAzMS4yIDYuMDggNi43MyAxLjIgMTMuNDUgMi40MyAyMC4zNSAzLjI1YTM1NC44MyAzNTQuODMgMCAwMDEyOC44MS03LjQgMjQ4Ljg4IDI0OC44OCAwIDAwMTAuMTUgNTUuMDYgNDI1LjY0IDQyNS42NCAwIDAxLTk2LjE3IDExLjI0IDQxNy45OCA0MTcuOTggMCAwMS04Ni40LTkuNTJMMjE2LjUyIDgxNy40YTI3LjYyIDI3LjYyIDAgMDEtMjkuOTgtMy4xNCAyOC4wMiAyOC4wMiAwIDAxLTkuNjctMjguNjFsMjIuNC05MC4yNEEyOTIuMjYgMjkyLjI2IDAgMDE2NCA0NTYuMjFDNjQgMjg1Ljk4IDIyNyAxNDggNDI4LjA5IDE0OGMxOTAuOTMgMCAzNDcuMjkgMTI0LjUzIDM2Mi41MiAyODIuODJhMjQ0Ljk3IDI0NC45NyAwIDAwLTI2LjQ3LTIuNjJjLTkuOS4zOC0xOS43OSAxLjMxLTI5LjYgMi44OHptLTExNi4zIDE5OC44MWExMzUuNzYgMTM1Ljc2IDAgMDA0Ny4wNS0xOS4wNCAxMTQuMjQgMTE0LjI0IDAgMDE1MS40NS0zMSA3Ni40NyA3Ni40NyAwIDAxLTI0LjUgNDUuMzQgMTY5LjQ4IDE2OS40OCAwIDAwLTIzLjQgNTUuMDUgNTAuNDEgNTAuNDEgMCAwMS0xMDAuOC4yMyA1MC40MSA1MC40MSAwIDAxNTAuMi01MC41OG05MC44IDEyMS4zMmExNjguNiAxNjguNiAwIDAwNTQuNjYgMjMuOSA1MC40NCA1MC40NCAwIDAxMzUuNjQgODYuMDggNTAuMzggNTAuMzggMCAwMS04Ni4wNC0zNS42NiAxMzYuNzQgMTM2Ljc0IDAgMDAtMTguNjctNDcuMjggMTE0LjcxIDExNC43MSAwIDAxLTMwLjU0LTUxLjggNzYgNzYgMCAwMTQ0Ljk1IDI1LjA2eiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
