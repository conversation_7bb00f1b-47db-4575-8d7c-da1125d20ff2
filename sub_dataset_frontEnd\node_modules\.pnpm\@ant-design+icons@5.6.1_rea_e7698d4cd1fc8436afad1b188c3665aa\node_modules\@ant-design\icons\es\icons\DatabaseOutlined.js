import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import DatabaseOutlinedSvg from "@ant-design/icons-svg/es/asn/DatabaseOutlined";
import AntdIcon from "../components/AntdIcon";
var DatabaseOutlined = function DatabaseOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: DatabaseOutlinedSvg
  }));
};

/**![database](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgzMiA2NEgxOTJjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjgzMmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg2NDBjMTcuNyAwIDMyLTE0LjMgMzItMzJWOTZjMC0xNy43LTE0LjMtMzItMzItMzJ6bS02MDAgNzJoNTYwdjIwOEgyMzJWMTM2em01NjAgNDgwSDIzMlY0MDhoNTYwdjIwOHptMCAyNzJIMjMyVjY4MGg1NjB2MjA4ek0zMDQgMjQwYTQwIDQwIDAgMTA4MCAwIDQwIDQwIDAgMTAtODAgMHptMCAyNzJhNDAgNDAgMCAxMDgwIDAgNDAgNDAgMCAxMC04MCAwem0wIDI3MmE0MCA0MCAwIDEwODAgMCA0MCA0MCAwIDEwLTgwIDB6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(DatabaseOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'DatabaseOutlined';
}
export default RefIcon;