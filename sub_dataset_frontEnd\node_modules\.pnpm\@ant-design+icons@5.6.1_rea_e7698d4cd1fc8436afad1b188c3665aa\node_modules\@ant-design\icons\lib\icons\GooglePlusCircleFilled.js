"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _GooglePlusCircleFilled = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/GooglePlusCircleFilled"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var GooglePlusCircleFilled = function GooglePlusCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _GooglePlusCircleFilled.default
  }));
};

/**![google-plus-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0zNi41IDU1OC44Yy00My45IDYxLjgtMTMyLjEgNzkuOC0yMDAuOSA1My4zLTY5LTI2LjMtMTE4LTk5LjItMTEyLjEtMTczLjUgMS41LTkwLjkgODUuMi0xNzAuNiAxNzYuMS0xNjcuNSA0My42LTIgODQuNiAxNi45IDExOCA0My42LTE0LjMgMTYuMi0yOSAzMS44LTQ0LjggNDYuMy00MC4xLTI3LjctOTcuMi0zNS42LTEzNy4zLTMuNi01Ny40IDM5LjctNjAgMTMzLjQtNC44IDE3Ni4xIDUzLjcgNDguNyAxNTUuMiAyNC41IDE3MC4xLTUwLjEtMzMuNi0uNS02Ny40IDAtMTAxLTEuMS0uMS0yMC4xLS4yLTQwLjEtLjEtNjAuMiA1Ni4yLS4yIDExMi41LS4zIDE2OC44LjIgMy4zIDQ3LjMtMyA5Ny41LTMyIDEzNi41ek03OTEgNTM2LjVjLTE2LjguMi0zMy42LjMtNTAuNC40LS4yIDE2LjgtLjMgMzMuNi0uMyA1MC40SDY5MGMtLjItMTYuOC0uMi0zMy41LS4zLTUwLjMtMTYuOC0uMi0zMy42LS4zLTUwLjQtLjV2LTUwLjFjMTYuOC0uMiAzMy42LS4zIDUwLjQtLjMuMS0xNi44LjMtMzMuNi40LTUwLjRoNTAuMmwuMyA1MC40YzE2LjguMiAzMy42LjIgNTAuNC4zdjUwLjF6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(GooglePlusCircleFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'GooglePlusCircleFilled';
}
var _default = exports.default = RefIcon;