# PagePlug 数据集管理微前端应用

这是一个基于React + TypeScript + Webpack Module Federation构建的数据集管理微前端应用，用于管理API接口、数据库查询、JavaScript对象和数据集合。

## 🚀 功能特性

- **API管理**: 支持REST API、GraphQL接口的创建、编辑和执行
- **数据库查询**: 可视化查询构建器，支持SQL和NoSQL数据库
- **JavaScript对象**: 管理可重用的JavaScript函数和对象
- **数据集合**: 将相关的API和查询组织成集合，支持批量执行
- **查询模板**: 预定义的查询模板，快速生成常用查询
- **微前端架构**: 基于Module Federation，可独立部署和集成

## 🛠️ 技术栈

- **框架**: React 18 + TypeScript
- **状态管理**: Redux Toolkit + React Query
- **UI组件**: Ant Design + Styled Components
- **样式**: TailwindCSS + CSS-in-JS
- **构建工具**: Webpack 5 + Module Federation
- **代码编辑器**: Monaco Editor
- **测试**: Jest + React Testing Library
- **代码质量**: ESLint + Prettier + Husky

## 📦 项目结构

```
sub_dataset_frontEnd/
├── public/                 # 静态资源
├── src/
│   ├── components/        # 组件
│   │   ├── layout/       # 布局组件
│   │   └── shared/       # 共享组件
│   ├── pages/            # 页面组件
│   ├── store/            # Redux状态管理
│   │   ├── slices/       # Redux切片
│   │   └── middleware/   # 中间件
│   ├── services/         # API服务
│   │   ├── api/          # API接口
│   │   └── mockData/     # 模拟数据
│   ├── types/            # TypeScript类型定义
│   ├── hooks/            # 自定义Hooks
│   ├── styles/           # 样式文件
│   ├── routes/           # 路由配置
│   └── utils/            # 工具函数
├── webpack.config.js      # Webpack配置
├── tailwind.config.js     # TailwindCSS配置
├── tsconfig.json         # TypeScript配置
└── package.json          # 项目依赖
```

## 🚀 快速开始

### 安装依赖

```bash
cd sub_dataset_frontEnd
npm install
```

### 开发模式

```bash
npm run dev
```

应用将在 http://localhost:3002 启动

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

### 运行测试

```bash
# 运行所有测试
npm test

# 监听模式
npm run test:watch

# 生成覆盖率报告
npm run test:coverage
```

### 代码检查和格式化

```bash
# 检查代码质量
npm run lint

# 自动修复代码问题
npm run lint:fix

# 格式化代码
npm run format

# 检查格式
npm run format:check
```

## 🔧 配置说明

### 环境变量

创建 `.env` 文件配置环境变量：

```env
# 应用端口
PORT=3002

# API基础URL
REACT_APP_API_BASE_URL=http://localhost:8080

# 微前端模式
REACT_APP_MICRO_FRONTEND=true

# 调试模式
REACT_APP_DEBUG=true
```

### 微前端集成

作为微前端应用，可以通过以下方式集成到主应用：

```javascript
// 在主应用中加载
import { mount } from 'dataset-app/DatasetApp';

// 挂载到指定容器
const unmount = mount(document.getElementById('dataset-container'), {
  basename: '/dataset',
  initialData: {},
  onNavigate: (path) => console.log('导航到:', path)
});

// 卸载应用
unmount();
```

### Module Federation配置

```javascript
// webpack.config.js
new ModuleFederationPlugin({
  name: 'datasetApp',
  filename: 'remoteEntry.js',
  exposes: {
    './DatasetApp': './src/App',
    './DatasetRoutes': './src/routes',
    './DatasetStore': './src/store',
  },
  shared: {
    react: { singleton: true },
    'react-dom': { singleton: true },
    antd: { singleton: true },
  },
})
```

## 📱 功能模块

### 1. API管理
- 创建和编辑REST API、GraphQL接口
- 配置请求参数、头部、认证信息
- 实时执行和调试API
- 查看响应结果和性能指标

### 2. 数据集合
- 将相关API组织成集合
- 配置集合变量和执行顺序
- 批量执行集合中的所有API
- 查看集合执行结果和统计

### 3. 查询构建器
- 可视化SQL查询构建
- 支持多种数据库类型
- 语法高亮和自动补全
- 查询结果预览和导出

### 4. JavaScript对象
- 创建可重用的JS函数
- 数据处理和业务逻辑
- 与API和查询的集成
- 调试和测试工具

## 🎨 主题定制

应用支持主题定制，可以通过以下方式修改：

```typescript
// 在主应用中设置主题
import { ThemeProvider } from 'styled-components';
import { lightTheme, darkTheme } from 'dataset-app/theme';

<ThemeProvider theme={lightTheme}>
  <DatasetApp />
</ThemeProvider>
```

## 🧪 测试策略

- **单元测试**: 组件和工具函数测试
- **集成测试**: Redux状态管理和API集成测试
- **端到端测试**: 用户流程测试
- **视觉回归测试**: UI组件视觉一致性测试

## 📈 性能优化

- **代码分割**: 基于路由的懒加载
- **Bundle分析**: 使用webpack-bundle-analyzer
- **缓存策略**: 合理的缓存配置
- **Tree Shaking**: 移除未使用的代码
- **压缩优化**: 生产环境代码压缩

## 🔒 安全考虑

- **XSS防护**: 输入验证和输出编码
- **CSRF防护**: Token验证
- **内容安全策略**: CSP头部配置
- **依赖安全**: 定期更新和漏洞扫描

## 🚀 部署指南

### Docker部署

```dockerfile
FROM nginx:alpine
COPY dist/ /usr/share/nginx/html/
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### CI/CD配置

```yaml
# .github/workflows/deploy.yml
name: Deploy Dataset App
on:
  push:
    branches: [main]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '16'
      - name: Install dependencies
        run: npm ci
      - name: Run tests
        run: npm test
      - name: Build
        run: npm run build
      - name: Deploy
        run: npm run deploy
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系我们

- 项目维护者: PagePlug Team
- 邮箱: <EMAIL>
- 文档: [项目文档](./doc/)

## 🔄 更新日志

查看 [CHANGELOG.md](CHANGELOG.md) 了解版本更新历史。
