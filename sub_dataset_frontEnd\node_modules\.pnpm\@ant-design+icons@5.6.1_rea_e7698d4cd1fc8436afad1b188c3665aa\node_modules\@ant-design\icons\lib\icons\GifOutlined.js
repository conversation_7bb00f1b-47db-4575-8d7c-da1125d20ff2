"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _GifOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/GifOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var GifOutlined = function GifOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _GifOutlined.default
  }));
};

/**![gif](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik05NDQgMjk5SDY5MmMtNC40IDAtOCAzLjYtOCA4djQwNmMwIDQuNCAzLjYgOCA4IDhoNTkuMmM0LjQgMCA4LTMuNiA4LThWNTQ5LjloMTY4LjJjNC40IDAgOC0zLjYgOC04VjQ5NWMwLTQuNC0zLjYtOC04LThINzU5LjJWMzY0LjJIOTQ0YzQuNCAwIDgtMy42IDgtOFYzMDdjMC00LjQtMy42LTgtOC04em0tMzU2IDFoLTU2Yy00LjQgMC04IDMuNi04IDh2NDA2YzAgNC40IDMuNiA4IDggOGg1NmM0LjQgMCA4LTMuNiA4LThWMzA4YzAtNC40LTMuNi04LTgtOHpNNDUyIDUwMC45SDI5MC41Yy00LjQgMC04IDMuNi04IDh2NDMuN2MwIDQuNCAzLjYgOCA4IDhoOTQuOWwtLjMgOC45Yy0xLjIgNTguOC00NS42IDk4LjUtMTEwLjkgOTguNS03Ni4yIDAtMTIzLjktNTkuNy0xMjMuOS0xNTYuNyAwLTk1LjggNDYuOC0xNTUuMiAxMjEuNS0xNTUuMiA1NC44IDAgOTMuMSAyNi45IDEwOC41IDc1LjRoNzYuMmMtMTMuNi04Ny4yLTg2LTE0My40LTE4NC43LTE0My40QzE1MCAyODggNzIgMzc1LjIgNzIgNTExLjkgNzIgNjUwLjIgMTQ5LjEgNzM2IDI3MyA3MzZjMTE0LjEgMCAxODctNzAuNyAxODctMTgxLjZ2LTQ1LjVjMC00LjQtMy42LTgtOC04eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(GifOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'GifOutlined';
}
var _default = exports.default = RefIcon;