import { DefaultTheme } from 'styled-components';

// 主题接口定义
export interface DatasetTheme {
  colors: {
    primary: string;
    success: string;
    warning: string;
    error: string;
    text: {
      primary: string;
      secondary: string;
      disabled: string;
      inverse: string;
    };
    background: {
      primary: string;
      secondary: string;
      disabled: string;
      overlay: string;
    };
    border: {
      primary: string;
      light: string;
      focus: string;
    };
  };
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    xxl: string;
  };
  borderRadius: {
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  shadows: {
    card: string;
    cardHover: string;
    modal: string;
    dropdown: string;
  };
  typography: {
    fontFamily: string;
    fontSize: {
      xs: string;
      sm: string;
      base: string;
      lg: string;
      xl: string;
      '2xl': string;
      '3xl': string;
    };
    fontWeight: {
      normal: number;
      medium: number;
      semibold: number;
      bold: number;
    };
    lineHeight: {
      tight: number;
      normal: number;
      relaxed: number;
    };
  };
  transitions: {
    fast: string;
    normal: string;
    slow: string;
  };
  zIndex: {
    dropdown: number;
    modal: number;
    tooltip: number;
    notification: number;
  };
}

// 亮色主题
export const lightTheme: DatasetTheme = {
  colors: {
    primary: '#1890ff',
    success: '#52c41a',
    warning: '#faad14',
    error: '#ff4d4f',
    text: {
      primary: '#262626',
      secondary: '#8c8c8c',
      disabled: '#bfbfbf',
      inverse: '#ffffff',
    },
    background: {
      primary: '#ffffff',
      secondary: '#fafafa',
      disabled: '#f5f5f5',
      overlay: 'rgba(0, 0, 0, 0.45)',
    },
    border: {
      primary: '#d9d9d9',
      light: '#f0f0f0',
      focus: '#40a9ff',
    },
  },
  spacing: {
    xs: '4px',
    sm: '8px',
    md: '16px',
    lg: '24px',
    xl: '32px',
    xxl: '48px',
  },
  borderRadius: {
    sm: '2px',
    md: '6px',
    lg: '8px',
    xl: '12px',
  },
  shadows: {
    card: '0 2px 8px rgba(0, 0, 0, 0.15)',
    cardHover: '0 4px 12px rgba(0, 0, 0, 0.15)',
    modal: '0 4px 12px rgba(0, 0, 0, 0.15)',
    dropdown: '0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05)',
  },
  typography: {
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif',
    fontSize: {
      xs: '12px',
      sm: '14px',
      base: '14px',
      lg: '16px',
      xl: '18px',
      '2xl': '20px',
      '3xl': '24px',
    },
    fontWeight: {
      normal: 400,
      medium: 500,
      semibold: 600,
      bold: 700,
    },
    lineHeight: {
      tight: 1.25,
      normal: 1.5715,
      relaxed: 1.75,
    },
  },
  transitions: {
    fast: '0.15s ease',
    normal: '0.3s ease',
    slow: '0.5s ease',
  },
  zIndex: {
    dropdown: 1000,
    modal: 1050,
    tooltip: 1060,
    notification: 1070,
  },
};

// 暗色主题
export const darkTheme: DatasetTheme = {
  ...lightTheme,
  colors: {
    primary: '#1890ff',
    success: '#52c41a',
    warning: '#faad14',
    error: '#ff4d4f',
    text: {
      primary: '#ffffff',
      secondary: '#a6a6a6',
      disabled: '#595959',
      inverse: '#262626',
    },
    background: {
      primary: '#1f1f1f',
      secondary: '#141414',
      disabled: '#262626',
      overlay: 'rgba(0, 0, 0, 0.65)',
    },
    border: {
      primary: '#434343',
      light: '#303030',
      focus: '#40a9ff',
    },
  },
  shadows: {
    card: '0 2px 8px rgba(0, 0, 0, 0.45)',
    cardHover: '0 4px 12px rgba(0, 0, 0, 0.45)',
    modal: '0 4px 12px rgba(0, 0, 0, 0.45)',
    dropdown: '0 3px 6px -4px rgba(0, 0, 0, 0.32), 0 6px 16px 0 rgba(0, 0, 0, 0.28), 0 9px 28px 8px rgba(0, 0, 0, 0.25)',
  },
};

// 扩展styled-components的DefaultTheme
declare module 'styled-components' {
  export interface DefaultTheme extends DatasetTheme {}
}

// 主题工具函数
export const getThemeColor = (theme: DatasetTheme, colorPath: string): string => {
  const paths = colorPath.split('.');
  let value: any = theme.colors;
  
  for (const path of paths) {
    value = value[path];
    if (value === undefined) {
      console.warn(`Theme color path "${colorPath}" not found`);
      return theme.colors.primary;
    }
  }
  
  return value;
};

export const getThemeSpacing = (theme: DatasetTheme, size: keyof DatasetTheme['spacing']): string => {
  return theme.spacing[size];
};

export const getThemeBorderRadius = (theme: DatasetTheme, size: keyof DatasetTheme['borderRadius']): string => {
  return theme.borderRadius[size];
};

export const getThemeShadow = (theme: DatasetTheme, type: keyof DatasetTheme['shadows']): string => {
  return theme.shadows[type];
};

// CSS变量生成函数
export const generateCSSVariables = (theme: DatasetTheme): Record<string, string> => {
  return {
    '--dataset-primary': theme.colors.primary,
    '--dataset-success': theme.colors.success,
    '--dataset-warning': theme.colors.warning,
    '--dataset-error': theme.colors.error,
    '--dataset-text': theme.colors.text.primary,
    '--dataset-text-secondary': theme.colors.text.secondary,
    '--dataset-text-disabled': theme.colors.text.disabled,
    '--dataset-bg': theme.colors.background.primary,
    '--dataset-bg-secondary': theme.colors.background.secondary,
    '--dataset-bg-disabled': theme.colors.background.disabled,
    '--dataset-border': theme.colors.border.primary,
    '--dataset-border-light': theme.colors.border.light,
    '--dataset-border-radius': theme.borderRadius.md,
    '--dataset-shadow': theme.shadows.card,
    '--dataset-shadow-hover': theme.shadows.cardHover,
    '--dataset-shadow-modal': theme.shadows.modal,
    '--dataset-font-family': theme.typography.fontFamily,
    '--dataset-font-size': theme.typography.fontSize.base,
  };
};
