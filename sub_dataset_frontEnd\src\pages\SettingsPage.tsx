import React from 'react';
import { Card, Typography, Button } from 'antd';
import { SettingOutlined } from '@ant-design/icons';
import styled from 'styled-components';

import { PageContainer } from '@/styles/global';

const { Title, Paragraph } = Typography;

const ComingSoonCard = styled(Card)`
  text-align: center;
  max-width: 600px;
  margin: 60px auto;
  
  .coming-soon-icon {
    font-size: 64px;
    color: var(--dataset-text-secondary);
    margin-bottom: 24px;
  }
  
  .coming-soon-title {
    margin-bottom: 16px;
  }
  
  .coming-soon-description {
    color: var(--dataset-text-secondary);
    margin-bottom: 32px;
  }
`;

const SettingsPage: React.FC = () => {
  return (
    <PageContainer>
      <ComingSoonCard>
        <SettingOutlined className="coming-soon-icon" />
        <Title level={2} className="coming-soon-title">
          设置
        </Title>
        <Paragraph className="coming-soon-description">
          设置页面正在开发中，敬请期待！
          <br />
          您将能够配置应用程序的各种设置，包括主题、语言、通知等。
        </Paragraph>
        <Button type="default" disabled>
          配置设置（即将推出）
        </Button>
      </ComingSoonCard>
    </PageContainer>
  );
};

export default SettingsPage;
