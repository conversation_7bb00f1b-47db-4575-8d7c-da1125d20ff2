import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import CodeTwoToneSvg from "@ant-design/icons-svg/es/asn/CodeTwoTone";
import AntdIcon from "../components/AntdIcon";
var CodeTwoTone = function CodeTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: CodeTwoToneSvg
  }));
};

/**![code](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnptLTQwIDcyOEgxODRWMTg0aDY1NnY2NTZ6IiBmaWxsPSIjMTY3N2ZmIiAvPjxwYXRoIGQ9Ik0xODQgODQwaDY1NlYxODRIMTg0djY1NnptMzM5LjUtMjIzaDE4NWM0LjEgMCA3LjUgMy42IDcuNSA4djQ4YzAgNC40LTMuNCA4LTcuNSA4aC0xODVjLTQuMSAwLTcuNS0zLjYtNy41LTh2LTQ4YzAtNC40IDMuNC04IDcuNS04ek0zMDggNjEwLjNjMC0yLjMgMS4xLTQuNiAyLjktNi4xTDQyMC43IDUxMmwtMTA5LjgtOTIuMmE3LjYzIDcuNjMgMCAwMS0yLjktNi4xVjM1MWMwLTYuOCA3LjktMTAuNSAxMy4xLTYuMWwxOTIgMTYwLjljMy45IDMuMiAzLjkgOS4xIDAgMTIuM2wtMTkyIDE2MWMtNS4yIDQuNC0xMy4xLjctMTMuMS02LjF2LTYyLjd6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik0zMjEuMSA2NzkuMWwxOTItMTYxYzMuOS0zLjIgMy45LTkuMSAwLTEyLjNsLTE5Mi0xNjAuOUE3Ljk1IDcuOTUgMCAwMDMwOCAzNTF2NjIuN2MwIDIuNCAxIDQuNiAyLjkgNi4xTDQyMC43IDUxMmwtMTA5LjggOTIuMmE4LjEgOC4xIDAgMDAtMi45IDYuMVY2NzNjMCA2LjggNy45IDEwLjUgMTMuMSA2LjF6TTUxNiA2NzNjMCA0LjQgMy40IDggNy41IDhoMTg1YzQuMSAwIDcuNS0zLjYgNy41LTh2LTQ4YzAtNC40LTMuNC04LTcuNS04aC0xODVjLTQuMSAwLTcuNSAzLjYtNy41IDh2NDh6IiBmaWxsPSIjMTY3N2ZmIiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(CodeTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CodeTwoTone';
}
export default RefIcon;