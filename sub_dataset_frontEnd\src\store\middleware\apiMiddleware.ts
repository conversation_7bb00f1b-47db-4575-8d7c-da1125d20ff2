import { Middleware } from '@reduxjs/toolkit';
import { addNotification } from '../slices/uiSlice';

// API中间件，用于处理API调用的通用逻辑
export const apiMiddleware: Middleware = (store) => (next) => (action) => {
  // 检查是否是异步action
  if (action.type && action.type.includes('/')) {
    const [sliceName, actionName, status] = action.type.split('/');
    
    // 处理API调用失败的情况
    if (status === 'rejected') {
      const errorMessage = action.error?.message || '操作失败';
      
      // 根据不同的action类型显示不同的错误消息
      let title = '操作失败';
      switch (actionName) {
        case 'fetchActions':
          title = '获取Actions失败';
          break;
        case 'createAction':
          title = '创建Action失败';
          break;
        case 'updateAction':
          title = '更新Action失败';
          break;
        case 'deleteAction':
          title = '删除Action失败';
          break;
        case 'fetchCollections':
          title = '获取Collections失败';
          break;
        case 'createCollection':
          title = '创建Collection失败';
          break;
        case 'updateCollection':
          title = '更新Collection失败';
          break;
        case 'deleteCollection':
          title = '删除Collection失败';
          break;
        case 'executeAction':
          title = 'Action执行失败';
          break;
        case 'executeCollection':
          title = 'Collection执行失败';
          break;
        default:
          title = '操作失败';
      }
      
      // 显示错误通知
      store.dispatch(addNotification({
        type: 'error',
        title,
        message: errorMessage,
        duration: 5000,
      }));
    }
    
    // 处理API调用成功的情况
    if (status === 'fulfilled') {
      // 根据不同的action类型显示不同的成功消息
      let shouldShowNotification = false;
      let title = '操作成功';
      let message = '';
      
      switch (actionName) {
        case 'createAction':
          shouldShowNotification = true;
          title = '创建成功';
          message = 'Action已成功创建';
          break;
        case 'updateAction':
          shouldShowNotification = true;
          title = '更新成功';
          message = 'Action已成功更新';
          break;
        case 'deleteAction':
          shouldShowNotification = true;
          title = '删除成功';
          message = 'Action已成功删除';
          break;
        case 'createCollection':
          shouldShowNotification = true;
          title = '创建成功';
          message = 'Collection已成功创建';
          break;
        case 'updateCollection':
          shouldShowNotification = true;
          title = '更新成功';
          message = 'Collection已成功更新';
          break;
        case 'deleteCollection':
          shouldShowNotification = true;
          title = '删除成功';
          message = 'Collection已成功删除';
          break;
        case 'duplicateAction':
          shouldShowNotification = true;
          title = '复制成功';
          message = 'Action已成功复制';
          break;
        case 'duplicateCollection':
          shouldShowNotification = true;
          title = '复制成功';
          message = 'Collection已成功复制';
          break;
        default:
          shouldShowNotification = false;
      }
      
      if (shouldShowNotification) {
        store.dispatch(addNotification({
          type: 'success',
          title,
          message,
          duration: 3000,
        }));
      }
    }
  }
  
  return next(action);
};
