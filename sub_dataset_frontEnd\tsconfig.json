{"compilerOptions": {"target": "ES2020", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "sourceMap": true, "removeComments": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/components/*": ["src/components/*"], "@/pages/*": ["src/pages/*"], "@/hooks/*": ["src/hooks/*"], "@/utils/*": ["src/utils/*"], "@/types/*": ["src/types/*"], "@/services/*": ["src/services/*"], "@/store/*": ["src/store/*"], "@/styles/*": ["src/styles/*"]}}, "include": ["src/**/*", "src/**/*.tsx", "src/**/*.ts"], "exclude": ["node_modules", "dist", "build"]}