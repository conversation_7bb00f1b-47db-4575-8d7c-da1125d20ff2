import React from 'react';
import { createRoot } from 'react-dom/client';
import App from './App';
import './styles/index.css';

// 获取根元素
const container = document.getElementById('root');
if (!container) {
  throw new Error('Root element not found');
}

// 创建React根实例
const root = createRoot(container);

// 渲染应用
root.render(<App />);

// 热模块替换支持
if ((module as any).hot) {
  (module as any).hot.accept('./App', () => {
    const NextApp = require('./App').default;
    root.render(<NextApp />);
  });
}

// 性能监控（移除 web-vitals 依赖）
if (process.env.NODE_ENV === 'production') {
  console.log('Production mode - performance monitoring would be enabled here');
}

// 错误监控
window.addEventListener('error', (event) => {
  console.error('Global error:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason);
});
