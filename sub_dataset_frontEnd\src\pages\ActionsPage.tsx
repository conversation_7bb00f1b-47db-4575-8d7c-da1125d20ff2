import React, { useEffect, useState } from 'react';
import { 
  Table, 
  Button, 
  Input, 
  Select, 
  Space, 
  Tag, 
  Tooltip, 
  Card,
  Row,
  Col,
  Typography,
  Dropdown,
  Modal
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  FilterOutlined,
  PlayCircleOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  MoreOutlined,
  ApiOutlined,
  DatabaseOutlined,
  CodeOutlined
} from '@ant-design/icons';
import styled from 'styled-components';

import { useAppDispatch, useAppSelector } from '@/store';
import { fetchActions, deleteAction, duplicateAction } from '@/store/slices/actionSlice';
import { openModal, addNotification } from '@/store/slices/uiSlice';
import { Action, PluginType, ActionType } from '@/types/action';
import { PageContainer, CardContainer } from '@/styles/global';
import LoadingSpinner from '@/components/shared/LoadingSpinner';

const { Title, Text } = Typography;
const { Search } = Input;
const { Option } = Select;

// 样式组件
const PageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 0 24px;
  
  .header-left {
    .page-title {
      margin: 0;
      color: var(--dataset-text);
    }
    
    .page-description {
      color: var(--dataset-text-secondary);
      margin-top: 4px;
    }
  }
  
  .header-right {
    display: flex;
    gap: 12px;
  }
`;

const FilterBar = styled.div`
  display: flex;
  gap: 16px;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px 24px;
  background: var(--dataset-bg);
  border-radius: var(--dataset-border-radius);
  border: 1px solid var(--dataset-border);
  
  .filter-item {
    display: flex;
    align-items: center;
    gap: 8px;
    
    label {
      font-weight: 500;
      color: var(--dataset-text);
      white-space: nowrap;
    }
  }
`;

const StatsCards = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
  padding: 0 24px;
`;

const StatCard = styled(Card)`
  .ant-card-body {
    padding: 20px;
  }
  
  .stat-icon {
    font-size: 24px;
    margin-bottom: 8px;
    
    &.api { color: #1890ff; }
    &.db { color: #52c41a; }
    &.js { color: #faad14; }
  }
  
  .stat-number {
    font-size: 24px;
    font-weight: 600;
    color: var(--dataset-text);
    margin-bottom: 4px;
  }
  
  .stat-label {
    color: var(--dataset-text-secondary);
    font-size: 14px;
  }
`;

// 插件类型图标映射
const getPluginIcon = (pluginType: PluginType) => {
  switch (pluginType) {
    case PluginType.API:
      return <ApiOutlined />;
    case PluginType.DB:
      return <DatabaseOutlined />;
    case PluginType.JS:
      return <CodeOutlined />;
    default:
      return <ApiOutlined />;
  }
};

// 插件类型颜色映射
const getPluginColor = (pluginType: PluginType) => {
  switch (pluginType) {
    case PluginType.API:
      return 'blue';
    case PluginType.DB:
      return 'green';
    case PluginType.JS:
      return 'orange';
    default:
      return 'default';
  }
};

// Actions页面组件
const ActionsPage: React.FC<{ initialData?: any }> = ({ initialData }) => {
  const dispatch = useAppDispatch();
  const { 
    items: actions, 
    loading, 
    error, 
    pagination,
    filters 
  } = useAppSelector(state => state.actions);

  const [searchText, setSearchText] = useState('');
  const [selectedPluginType, setSelectedPluginType] = useState<string>('');
  const [selectedActionType, setSelectedActionType] = useState<string>('');

  // 加载Actions数据
  useEffect(() => {
    if (!initialData) {
      dispatch(fetchActions({
        applicationId: 'app_1', // 模拟应用ID
        filters: {
          search: searchText,
          pluginType: selectedPluginType || undefined,
          actionType: selectedActionType || undefined,
          page: pagination.page,
          pageSize: pagination.pageSize,
        }
      }));
    }
  }, [dispatch, searchText, selectedPluginType, selectedActionType, pagination.page, pagination.pageSize, initialData]);

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchText(value);
  };

  // 处理删除Action
  const handleDelete = (actionId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个Action吗？此操作不可撤销。',
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await dispatch(deleteAction(actionId)).unwrap();
          dispatch(addNotification({
            type: 'success',
            title: '删除成功',
            message: 'Action已成功删除',
          }));
        } catch (error) {
          dispatch(addNotification({
            type: 'error',
            title: '删除失败',
            message: error as string,
          }));
        }
      },
    });
  };

  // 处理复制Action
  const handleDuplicate = async (actionId: string) => {
    try {
      await dispatch(duplicateAction({ id: actionId })).unwrap();
      dispatch(addNotification({
        type: 'success',
        title: '复制成功',
        message: 'Action已成功复制',
      }));
    } catch (error) {
      dispatch(addNotification({
        type: 'error',
        title: '复制失败',
        message: error as string,
      }));
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Action) => (
        <Space>
          {getPluginIcon(record.pluginType)}
          <span style={{ fontWeight: 500 }}>{text}</span>
        </Space>
      ),
    },
    {
      title: '类型',
      dataIndex: 'pluginType',
      key: 'pluginType',
      render: (pluginType: PluginType) => (
        <Tag color={getPluginColor(pluginType)}>
          {pluginType}
        </Tag>
      ),
    },
    {
      title: '方法/查询',
      key: 'method',
      render: (record: Action) => {
        if (record.actionConfiguration.httpMethod) {
          return (
            <Tag color="blue">
              {record.actionConfiguration.httpMethod}
            </Tag>
          );
        }
        if (record.actionType === ActionType.SQL) {
          return <Tag color="green">SQL</Tag>;
        }
        if (record.actionType === ActionType.JAVASCRIPT) {
          return <Tag color="orange">JS</Tag>;
        }
        return '-';
      },
    },
    {
      title: '路径/查询',
      key: 'path',
      render: (record: Action) => {
        const path = record.actionConfiguration.path || 
                    (record.actionConfiguration.query ? '查询语句' : '-');
        return (
          <Tooltip title={path}>
            <Text ellipsis style={{ maxWidth: 200 }}>
              {path}
            </Text>
          </Tooltip>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'isValid',
      key: 'isValid',
      render: (isValid: boolean) => (
        <Tag color={isValid ? 'success' : 'error'}>
          {isValid ? '有效' : '无效'}
        </Tag>
      ),
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      render: (date: string) => new Date(date).toLocaleString('zh-CN'),
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: Action) => (
        <Space>
          <Tooltip title="执行">
            <Button 
              type="text" 
              icon={<PlayCircleOutlined />} 
              size="small"
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              size="small"
            />
          </Tooltip>
          <Dropdown
            menu={{
              items: [
                {
                  key: 'duplicate',
                  label: '复制',
                  icon: <CopyOutlined />,
                  onClick: () => handleDuplicate(record.id),
                },
                {
                  key: 'delete',
                  label: '删除',
                  icon: <DeleteOutlined />,
                  danger: true,
                  onClick: () => handleDelete(record.id),
                },
              ],
            }}
            trigger={['click']}
          >
            <Button type="text" icon={<MoreOutlined />} size="small" />
          </Dropdown>
        </Space>
      ),
    },
  ];

  // 统计数据
  const actionsList = Object.values(actions);
  const stats = {
    total: actionsList.length,
    api: actionsList.filter(a => a.pluginType === PluginType.API).length,
    db: actionsList.filter(a => a.pluginType === PluginType.DB).length,
    js: actionsList.filter(a => a.pluginType === PluginType.JS).length,
  };

  if (loading && actionsList.length === 0) {
    return <LoadingSpinner size="large" tip="正在加载Actions..." />;
  }

  return (
    <PageContainer>
      {/* 页面头部 */}
      <PageHeader>
        <div className="header-left">
          <Title level={2} className="page-title">API管理</Title>
          <div className="page-description">
            管理和配置应用程序的API接口、数据库查询和JavaScript对象
          </div>
        </div>
        <div className="header-right">
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => dispatch(openModal('createAction'))}
          >
            创建API
          </Button>
        </div>
      </PageHeader>

      {/* 统计卡片 */}
      <StatsCards>
        <StatCard>
          <div className="stat-icon api">
            <ApiOutlined />
          </div>
          <div className="stat-number">{stats.api}</div>
          <div className="stat-label">REST API</div>
        </StatCard>
        <StatCard>
          <div className="stat-icon db">
            <DatabaseOutlined />
          </div>
          <div className="stat-number">{stats.db}</div>
          <div className="stat-label">数据库查询</div>
        </StatCard>
        <StatCard>
          <div className="stat-icon js">
            <CodeOutlined />
          </div>
          <div className="stat-number">{stats.js}</div>
          <div className="stat-label">JS对象</div>
        </StatCard>
        <StatCard>
          <div className="stat-icon">
            <ApiOutlined />
          </div>
          <div className="stat-number">{stats.total}</div>
          <div className="stat-label">总计</div>
        </StatCard>
      </StatsCards>

      {/* 过滤栏 */}
      <CardContainer>
        <FilterBar>
          <div className="filter-item">
            <label>搜索:</label>
            <Search
              placeholder="搜索Action名称或路径"
              allowClear
              style={{ width: 300 }}
              onSearch={handleSearch}
            />
          </div>
          <div className="filter-item">
            <label>插件类型:</label>
            <Select
              placeholder="选择插件类型"
              allowClear
              style={{ width: 150 }}
              value={selectedPluginType}
              onChange={setSelectedPluginType}
            >
              <Option value={PluginType.API}>API</Option>
              <Option value={PluginType.DB}>数据库</Option>
              <Option value={PluginType.JS}>JavaScript</Option>
            </Select>
          </div>
          <div className="filter-item">
            <label>Action类型:</label>
            <Select
              placeholder="选择Action类型"
              allowClear
              style={{ width: 150 }}
              value={selectedActionType}
              onChange={setSelectedActionType}
            >
              <Option value={ActionType.REST_API}>REST API</Option>
              <Option value={ActionType.GRAPHQL}>GraphQL</Option>
              <Option value={ActionType.SQL}>SQL</Option>
              <Option value={ActionType.JAVASCRIPT}>JavaScript</Option>
            </Select>
          </div>
        </FilterBar>

        {/* Actions表格 */}
        <Table
          columns={columns}
          dataSource={actionsList}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.page,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
        />
      </CardContainer>
    </PageContainer>
  );
};

export default ActionsPage;
