import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

import { Action, ActionCreateRequest, ActionUpdateRequest, ValidationResult } from '@/types/action';
import { PaginatedResponse } from '@/types/common';
import { actionAPI } from '@/services/api/actionAPI';

// 异步Actions
export const fetchActions = createAsyncThunk(
  'actions/fetchActions',
  async (params: {
    applicationId: string;
    filters?: {
      pluginType?: string;
      actionType?: string;
      datasourceId?: string;
      search?: string;
      page?: number;
      pageSize?: number;
    };
  }) => {
    const response = await actionAPI.getActions(params.applicationId, params.filters);
    if (!response.success) {
      throw new Error(response.message || 'Failed to fetch actions');
    }
    return response.data;
  }
);

export const fetchAction = createAsyncThunk(
  'actions/fetchAction',
  async (id: string) => {
    const response = await actionAPI.getAction(id);
    if (!response.success) {
      throw new Error(response.message || 'Failed to fetch action');
    }
    return response.data;
  }
);

export const createAction = createAsyncThunk(
  'actions/createAction',
  async (request: ActionCreateRequest) => {
    const response = await actionAPI.createAction(request);
    if (!response.success) {
      throw new Error(response.message || 'Failed to create action');
    }
    return response.data;
  }
);

export const updateAction = createAsyncThunk(
  'actions/updateAction',
  async (params: { id: string; data: ActionUpdateRequest }) => {
    const response = await actionAPI.updateAction(params.id, params.data);
    if (!response.success) {
      throw new Error(response.message || 'Failed to update action');
    }
    return response.data;
  }
);

export const deleteAction = createAsyncThunk(
  'actions/deleteAction',
  async (id: string) => {
    const response = await actionAPI.deleteAction(id);
    if (!response.success) {
      throw new Error(response.message || 'Failed to delete action');
    }
    return id;
  }
);

export const duplicateAction = createAsyncThunk(
  'actions/duplicateAction',
  async (params: { id: string; newName?: string }) => {
    const response = await actionAPI.duplicateAction(params.id, params.newName);
    if (!response.success) {
      throw new Error(response.message || 'Failed to duplicate action');
    }
    return response.data;
  }
);

export const validateAction = createAsyncThunk(
  'actions/validateAction',
  async (action: Action) => {
    const response = await actionAPI.validateAction(action);
    if (!response.success) {
      throw new Error(response.message || 'Failed to validate action');
    }
    return { actionId: action.id, result: response.data };
  }
);

export const batchDeleteActions = createAsyncThunk(
  'actions/batchDeleteActions',
  async (ids: string[]) => {
    const response = await actionAPI.batchDeleteActions(ids);
    if (!response.success) {
      throw new Error(response.message || 'Failed to delete actions');
    }
    return ids;
  }
);

// State接口
interface ActionState {
  items: Record<string, Action>;
  currentAction: Action | null;
  loading: boolean;
  error: string | null;
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  filters: {
    pluginType?: string;
    actionType?: string;
    datasourceId?: string;
    search?: string;
  };
  validationResults: Record<string, ValidationResult>;
  selectedActionIds: string[];
  sortBy: 'name' | 'createdAt' | 'updatedAt';
  sortOrder: 'asc' | 'desc';
}

const initialState: ActionState = {
  items: {},
  currentAction: null,
  loading: false,
  error: null,
  pagination: {
    page: 1,
    pageSize: 20,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false,
  },
  filters: {},
  validationResults: {},
  selectedActionIds: [],
  sortBy: 'updatedAt',
  sortOrder: 'desc',
};

// Slice定义
const actionSlice = createSlice({
  name: 'actions',
  initialState,
  reducers: {
    setCurrentAction: (state, action: PayloadAction<Action | null>) => {
      state.currentAction = action.payload;
    },
    
    updateActionInPlace: (state, action: PayloadAction<Partial<Action> & { id: string }>) => {
      const { id, ...updates } = action.payload;
      if (state.items[id]) {
        state.items[id] = { ...state.items[id], ...updates };
      }
      if (state.currentAction?.id === id) {
        state.currentAction = { ...state.currentAction, ...updates };
      }
    },
    
    setFilters: (state, action: PayloadAction<Partial<ActionState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
      // 重置分页
      state.pagination.page = 1;
    },
    
    clearFilters: (state) => {
      state.filters = {};
      state.pagination.page = 1;
    },
    
    setPagination: (state, action: PayloadAction<{ page?: number; pageSize?: number }>) => {
      if (action.payload.page !== undefined) {
        state.pagination.page = action.payload.page;
      }
      if (action.payload.pageSize !== undefined) {
        state.pagination.pageSize = action.payload.pageSize;
      }
    },
    
    setSorting: (state, action: PayloadAction<{ sortBy: ActionState['sortBy']; sortOrder: ActionState['sortOrder'] }>) => {
      state.sortBy = action.payload.sortBy;
      state.sortOrder = action.payload.sortOrder;
    },
    
    setSelectedActions: (state, action: PayloadAction<string[]>) => {
      state.selectedActionIds = action.payload;
    },
    
    toggleActionSelection: (state, action: PayloadAction<string>) => {
      const actionId = action.payload;
      const index = state.selectedActionIds.indexOf(actionId);
      if (index > -1) {
        state.selectedActionIds.splice(index, 1);
      } else {
        state.selectedActionIds.push(actionId);
      }
    },
    
    selectAllActions: (state) => {
      state.selectedActionIds = Object.keys(state.items);
    },
    
    clearSelection: (state) => {
      state.selectedActionIds = [];
    },
    
    clearError: (state) => {
      state.error = null;
    },
    
    clearValidationResults: (state) => {
      state.validationResults = {};
    },
  },
  
  extraReducers: (builder) => {
    builder
      // fetchActions
      .addCase(fetchActions.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchActions.fulfilled, (state, action) => {
        state.loading = false;
        const paginatedData = action.payload;
        
        // 更新items
        state.items = paginatedData.items.reduce((acc, action) => {
          acc[action.id] = action;
          return acc;
        }, {} as Record<string, Action>);
        
        // 更新分页信息
        state.pagination = {
          page: paginatedData.page,
          pageSize: paginatedData.pageSize,
          total: paginatedData.total,
          totalPages: paginatedData.totalPages,
          hasNext: paginatedData.hasNext,
          hasPrev: paginatedData.hasPrev,
        };
      })
      .addCase(fetchActions.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch actions';
      })
      
      // fetchAction
      .addCase(fetchAction.fulfilled, (state, action) => {
        const actionData = action.payload;
        state.items[actionData.id] = actionData;
        state.currentAction = actionData;
      })
      
      // createAction
      .addCase(createAction.fulfilled, (state, action) => {
        const newAction = action.payload;
        state.items[newAction.id] = newAction;
        state.currentAction = newAction;
      })
      
      // updateAction
      .addCase(updateAction.fulfilled, (state, action) => {
        const updatedAction = action.payload;
        state.items[updatedAction.id] = updatedAction;
        if (state.currentAction?.id === updatedAction.id) {
          state.currentAction = updatedAction;
        }
      })
      
      // deleteAction
      .addCase(deleteAction.fulfilled, (state, action) => {
        const deletedId = action.payload;
        delete state.items[deletedId];
        if (state.currentAction?.id === deletedId) {
          state.currentAction = null;
        }
        // 从选中列表中移除
        state.selectedActionIds = state.selectedActionIds.filter(id => id !== deletedId);
      })
      
      // duplicateAction
      .addCase(duplicateAction.fulfilled, (state, action) => {
        const duplicatedAction = action.payload;
        state.items[duplicatedAction.id] = duplicatedAction;
        state.currentAction = duplicatedAction;
      })
      
      // validateAction
      .addCase(validateAction.fulfilled, (state, action) => {
        const { actionId, result } = action.payload;
        state.validationResults[actionId] = result;
      })
      
      // batchDeleteActions
      .addCase(batchDeleteActions.fulfilled, (state, action) => {
        const deletedIds = action.payload;
        deletedIds.forEach(id => {
          delete state.items[id];
        });
        if (state.currentAction && deletedIds.includes(state.currentAction.id)) {
          state.currentAction = null;
        }
        state.selectedActionIds = [];
      });
  },
});

export const {
  setCurrentAction,
  updateActionInPlace,
  setFilters,
  clearFilters,
  setPagination,
  setSorting,
  setSelectedActions,
  toggleActionSelection,
  selectAllActions,
  clearSelection,
  clearError,
  clearValidationResults,
} = actionSlice.actions;

export { actionSlice };
