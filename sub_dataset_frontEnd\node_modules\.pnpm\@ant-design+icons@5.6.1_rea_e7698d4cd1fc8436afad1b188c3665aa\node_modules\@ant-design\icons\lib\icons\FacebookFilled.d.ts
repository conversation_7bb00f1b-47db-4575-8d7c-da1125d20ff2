import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![facebook](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnptLTkyLjQgMjMzLjVoLTYzLjljLTUwLjEgMC01OS44IDIzLjgtNTkuOCA1OC44djc3LjFoMTE5LjZsLTE1LjYgMTIwLjdoLTEwNFY5MTJINTM5LjJWNjAyLjJINDM0LjlWNDgxLjRoMTA0LjN2LTg5YzAtMTAzLjMgNjMuMS0xNTkuNiAxNTUuMy0xNTkuNiA0NC4yIDAgODIuMSAzLjMgOTMuMiA0Ljh2MTA3Ljl6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
