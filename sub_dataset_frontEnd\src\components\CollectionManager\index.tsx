import React from 'react';
import { Card, Typography, Button } from 'antd';
import { FolderOutlined } from '@ant-design/icons';
import styled from 'styled-components';

const { Title, Paragraph } = Typography;

const ManagerContainer = styled(Card)`
  text-align: center;
  max-width: 600px;
  margin: 60px auto;
  
  .manager-icon {
    font-size: 64px;
    color: var(--dataset-success);
    margin-bottom: 24px;
  }
  
  .manager-title {
    margin-bottom: 16px;
  }
  
  .manager-description {
    color: var(--dataset-text-secondary);
    margin-bottom: 32px;
  }
`;

interface CollectionManagerProps {
  collectionId?: string;
  onSave?: (data: any) => void;
  onCancel?: () => void;
}

const CollectionManager: React.FC<CollectionManagerProps> = ({ collectionId, onSave, onCancel }) => {
  return (
    <ManagerContainer>
      <FolderOutlined className="manager-icon" />
      <Title level={2} className="manager-title">
        集合管理器
      </Title>
      <Paragraph className="manager-description">
        集合管理器组件正在开发中，敬请期待！
        <br />
        您将能够管理数据集合，配置执行顺序和变量。
      </Paragraph>
      <Button type="primary" disabled>
        开始管理集合（即将推出）
      </Button>
    </ManagerContainer>
  );
};

export default CollectionManager;
