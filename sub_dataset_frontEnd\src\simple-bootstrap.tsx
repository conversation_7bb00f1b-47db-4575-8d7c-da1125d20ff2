import React from 'react';
import { createRoot } from 'react-dom/client';
import SimpleTestApp from './simple-app';
import './styles/index.css';

// 获取根元素
const container = document.getElementById('root');
if (!container) {
  throw new Error('Root element not found');
}

// 创建React根实例
const root = createRoot(container);

// 渲染简单测试应用
root.render(<SimpleTestApp />);

console.log('🎉 Simple test app started successfully!');
