import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import BankOutlinedSvg from "@ant-design/icons-svg/es/asn/BankOutlined";
import AntdIcon from "../components/AntdIcon";
var BankOutlined = function BankOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: BankOutlinedSvg
  }));
};

/**![bank](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg5NCA0NjJjMzAuOSAwIDQzLjgtMzkuNyAxOC43LTU4TDUzMC44IDEyNi4yYTMxLjgxIDMxLjgxIDAgMDAtMzcuNiAwTDExMS4zIDQwNGMtMjUuMSAxOC4yLTEyLjIgNTggMTguOCA1OEgxOTJ2Mzc0aC03MmMtNC40IDAtOCAzLjYtOCA4djUyYzAgNC40IDMuNiA4IDggOGg3ODRjNC40IDAgOC0zLjYgOC04di01MmMwLTQuNC0zLjYtOC04LThoLTcyVjQ2Mmg2MnpNNTEyIDE5Ni43bDI3MS4xIDE5Ny4ySDI0MC45TDUxMiAxOTYuN3pNMjY0IDQ2MmgxMTd2Mzc0SDI2NFY0NjJ6bTE4OSAwaDExN3YzNzRINDUzVjQ2MnptMzA3IDM3NEg2NDJWNDYyaDExOHYzNzR6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(BankOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'BankOutlined';
}
export default RefIcon;