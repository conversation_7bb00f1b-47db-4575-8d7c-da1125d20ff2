"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _EyeTwoTone = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/EyeTwoTone"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var EyeTwoTone = function EyeTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _EyeTwoTone.default
  }));
};

/**![eye](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgxLjggNTM3LjhhNjAuMyA2MC4zIDAgMDEwLTUxLjVDMTc2LjYgMjg2LjUgMzE5LjggMTg2IDUxMiAxODZjLTE5Mi4yIDAtMzM1LjQgMTAwLjUtNDMwLjIgMzAwLjNhNjAuMyA2MC4zIDAgMDAwIDUxLjVDMTc2LjYgNzM3LjUgMzE5LjkgODM4IDUxMiA4MzhjLTE5Mi4xIDAtMzM1LjQtMTAwLjUtNDMwLjItMzAwLjJ6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik01MTIgMjU4Yy0xNjEuMyAwLTI3OS40IDgxLjgtMzYyLjcgMjU0QzIzMi42IDY4NC4yIDM1MC43IDc2NiA1MTIgNzY2YzE2MS40IDAgMjc5LjUtODEuOCAzNjIuNy0yNTRDNzkxLjQgMzM5LjggNjczLjMgMjU4IDUxMiAyNTh6bS00IDQzMGMtOTcuMiAwLTE3Ni03OC44LTE3Ni0xNzZzNzguOC0xNzYgMTc2LTE3NiAxNzYgNzguOCAxNzYgMTc2LTc4LjggMTc2LTE3NiAxNzZ6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik05NDIuMiA0ODYuMkM4NDcuNCAyODYuNSA3MDQuMSAxODYgNTEyIDE4NmMtMTkyLjIgMC0zMzUuNCAxMDAuNS00MzAuMiAzMDAuM2E2MC4zIDYwLjMgMCAwMDAgNTEuNUMxNzYuNiA3MzcuNSAzMTkuOSA4MzggNTEyIDgzOGMxOTIuMiAwIDMzNS40LTEwMC41IDQzMC4yLTMwMC4zIDcuNy0xNi4yIDcuNy0zNSAwLTUxLjV6TTUxMiA3NjZjLTE2MS4zIDAtMjc5LjQtODEuOC0zNjIuNy0yNTRDMjMyLjYgMzM5LjggMzUwLjcgMjU4IDUxMiAyNThzMjc5LjQgODEuOCAzNjIuNyAyNTRDNzkxLjUgNjg0LjIgNjczLjQgNzY2IDUxMiA3NjZ6IiBmaWxsPSIjMTY3N2ZmIiAvPjxwYXRoIGQ9Ik01MDggMzM2Yy05Ny4yIDAtMTc2IDc4LjgtMTc2IDE3NnM3OC44IDE3NiAxNzYgMTc2IDE3Ni03OC44IDE3Ni0xNzYtNzguOC0xNzYtMTc2LTE3NnptMCAyODhjLTYxLjkgMC0xMTItNTAuMS0xMTItMTEyczUwLjEtMTEyIDExMi0xMTIgMTEyIDUwLjEgMTEyIDExMi01MC4xIDExMi0xMTIgMTEyeiIgZmlsbD0iIzE2NzdmZiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(EyeTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'EyeTwoTone';
}
var _default = exports.default = RefIcon;