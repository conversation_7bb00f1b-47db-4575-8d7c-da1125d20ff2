import React from 'react';
import { Card, Typography, Button } from 'antd';
import { FileTextOutlined, PlusOutlined } from '@ant-design/icons';
import styled from 'styled-components';

import { PageContainer } from '@/styles/global';

const { Title, Paragraph } = Typography;

const ComingSoonCard = styled(Card)`
  text-align: center;
  max-width: 600px;
  margin: 60px auto;
  
  .coming-soon-icon {
    font-size: 64px;
    color: var(--dataset-success);
    margin-bottom: 24px;
  }
  
  .coming-soon-title {
    margin-bottom: 16px;
  }
  
  .coming-soon-description {
    color: var(--dataset-text-secondary);
    margin-bottom: 32px;
  }
`;

const TemplatesPage: React.FC = () => {
  return (
    <PageContainer>
      <ComingSoonCard>
        <FileTextOutlined className="coming-soon-icon" />
        <Title level={2} className="coming-soon-title">
          查询模板
        </Title>
        <Paragraph className="coming-soon-description">
          查询模板功能正在开发中，敬请期待！
          <br />
          您将能够创建和使用预定义的查询模板，快速生成常用的API和数据库查询。
        </Paragraph>
        <Button type="primary" icon={<PlusOutlined />} disabled>
          创建模板（即将推出）
        </Button>
      </ComingSoonCard>
    </PageContainer>
  );
};

export default TemplatesPage;
