import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import DribbbleCircleFilledSvg from "@ant-design/icons-svg/es/asn/DribbbleCircleFilled";
import AntdIcon from "../components/AntdIcon";
var DribbbleCircleFilled = function DribbbleCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: DribbbleCircleFilledSvg
  }));
};

/**![dribbble-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTY3NS4xIDMyOC4zYTI0NS4yIDI0NS4yIDAgMDAtMjIwLjgtNTUuMWM2LjggOS4xIDUxLjUgNjkuOSA5MS44IDE0NCA4Ny41LTMyLjggMTI0LjUtODIuNiAxMjktODguOXpNNTU0IDU1Mi44Yy0xMzguNyA0OC4zLTE4OC42IDE0NC42LTE5MyAxNTMuNiA0MS43IDMyLjUgOTQuMSA1MS45IDE1MSA1MS45IDM0LjEgMCA2Ni42LTYuOSA5Ni4xLTE5LjUtMy43LTIxLjYtMTcuOS05Ni44LTUyLjUtMTg2LjZsLTEuNi42em00Ny43LTExLjljMzIuMiA4OC40IDQ1LjMgMTYwLjQgNDcuOCAxNzUuNCA1NS4yLTM3LjMgOTQuNS05Ni40IDEwNS40LTE2NC45LTguNC0yLjYtNzYuMS0yMi44LTE1My4yLTEwLjV6TTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0wIDczNmMtMTU4LjggMC0yODgtMTI5LjItMjg4LTI4OHMxMjkuMi0yODggMjg4LTI4OCAyODggMTI5LjIgMjg4IDI4OC0xMjkuMiAyODgtMjg4IDI4OHptNTMuMS0zNDYuMmM1LjcgMTEuNyAxMS4yIDIzLjYgMTYuMyAzNS42IDEuOCA0LjIgMy42IDguNCA1LjMgMTIuNyA4MS44LTEwLjMgMTYzLjIgNi4yIDE3MS4zIDcuOS0uNS01OC4xLTIxLjMtMTExLjQtNTUuNS0xNTMuMy01LjMgNy4xLTQ2LjUgNjAtMTM3LjQgOTcuMXpNNDk4LjYgNDMyYy00MC44LTcyLjUtODQuNy0xMzMuNC05MS4yLTE0Mi4zLTY4LjggMzIuNS0xMjAuMyA5NS45LTEzNi4yIDE3Mi4yIDExIC4yIDExMi40LjcgMjI3LjQtMjkuOXptMzAuNiA4Mi41YzMuMi0xIDYuNC0yIDkuNy0yLjktNi4yLTE0LTEyLjktMjgtMTkuOS00MS43LTEyMi44IDM2LjgtMjQyLjEgMzUuMi0yNTIuOCAzNS0uMSAyLjUtLjEgNS0uMSA3LjUgMCA2My4yIDIzLjkgMTIwLjkgNjMuMiAxNjQuNSA1LjUtOS42IDczLTEyMS40IDE5OS45LTE2Mi40eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(DribbbleCircleFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'DribbbleCircleFilled';
}
export default RefIcon;