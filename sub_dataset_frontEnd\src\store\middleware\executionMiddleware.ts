import { Middleware } from '@reduxjs/toolkit';
import { addDebugLog } from '../slices/executionSlice';

// 执行中间件，用于处理执行相关的逻辑
export const executionMiddleware: Middleware = (store) => (next) => (action) => {
  const state = store.getState();
  const debugMode = state.execution?.debugMode;
  
  // 如果开启了调试模式，记录执行相关的action
  if (debugMode && action.type && action.type.includes('execution/')) {
    const [, actionName, status] = action.type.split('/');
    
    if (status === 'pending') {
      store.dispatch(addDebugLog({
        level: 'info',
        message: `开始执行: ${actionName}`,
        data: action.meta?.arg,
      }));
    } else if (status === 'fulfilled') {
      store.dispatch(addDebugLog({
        level: 'info',
        message: `执行成功: ${actionName}`,
        data: action.payload,
      }));
    } else if (status === 'rejected') {
      store.dispatch(addDebugLog({
        level: 'error',
        message: `执行失败: ${actionName}`,
        data: action.error,
      }));
    }
  }
  
  return next(action);
};
