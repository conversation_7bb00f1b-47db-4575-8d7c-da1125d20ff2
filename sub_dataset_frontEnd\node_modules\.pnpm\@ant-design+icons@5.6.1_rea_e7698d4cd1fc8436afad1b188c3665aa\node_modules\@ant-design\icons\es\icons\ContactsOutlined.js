import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import ContactsOutlinedSvg from "@ant-design/icons-svg/es/asn/ContactsOutlined";
import AntdIcon from "../components/AntdIcon";
var ContactsOutlined = function ContactsOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: ContactsOutlinedSvg
  }));
};

/**![contacts](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTU5NC4zIDYwMS41YTExMS44IDExMS44IDAgMDAyOS4xLTc1LjVjMC02MS45LTQ5LjktMTEyLTExMS40LTExMnMtMTExLjQgNTAuMS0xMTEuNCAxMTJjMCAyOS4xIDExIDU1LjUgMjkuMSA3NS41YTE1OC4wOSAxNTguMDkgMCAwMC03NC42IDEyNi4xIDggOCAwIDAwOCA4LjRINDA3YzQuMiAwIDcuNi0zLjMgNy45LTcuNSAzLjgtNTAuNiA0Ni05MC41IDk3LjItOTAuNXM5My40IDQwIDk3LjIgOTAuNWMuMyA0LjIgMy43IDcuNSA3LjkgNy41SDY2MWE4IDggMCAwMDgtOC40Yy0yLjgtNTMuMy0zMi05OS43LTc0LjctMTI2LjF6TTUxMiA1NzhjLTI4LjUgMC01MS43LTIzLjMtNTEuNy01MnMyMy4yLTUyIDUxLjctNTIgNTEuNyAyMy4zIDUxLjcgNTItMjMuMiA1Mi01MS43IDUyem00MTYtMzU0SDc2OHYtNTZjMC00LjQtMy42LTgtOC04aC01NmMtNC40IDAtOCAzLjYtOCA4djU2SDU0OHYtNTZjMC00LjQtMy42LTgtOC04aC01NmMtNC40IDAtOCAzLjYtOCA4djU2SDMyOHYtNTZjMC00LjQtMy42LTgtOC04aC01NmMtNC40IDAtOCAzLjYtOCA4djU2SDk2Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY1NzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoODMyYzE3LjcgMCAzMi0xNC4zIDMyLTMyVjI1NmMwLTE3LjctMTQuMy0zMi0zMi0zMnptLTQwIDU2OEgxMzZWMjk2aDEyMHY1NmMwIDQuNCAzLjYgOCA4IDhoNTZjNC40IDAgOC0zLjYgOC04di01NmgxNDh2NTZjMCA0LjQgMy42IDggOCA4aDU2YzQuNCAwIDgtMy42IDgtOHYtNTZoMTQ4djU2YzAgNC40IDMuNiA4IDggOGg1NmM0LjQgMCA4LTMuNiA4LTh2LTU2aDEyMHY0OTZ6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(ContactsOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ContactsOutlined';
}
export default RefIcon;