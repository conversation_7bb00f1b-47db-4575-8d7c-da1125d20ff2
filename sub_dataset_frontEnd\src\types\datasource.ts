// 数据源相关类型定义

export enum DatasourceType {
  MYSQL = 'MYSQL',
  POSTGRESQL = 'POSTGRESQL',
  MONGODB = 'MONGODB',
  REDIS = 'REDIS',
  ELASTICSEARCH = 'ELASTICSEARCH',
  REST_API = 'REST_API',
  GRAPHQL = 'GRAPHQL',
  SQLITE = 'SQLITE',
  ORACLE = 'ORACLE',
  MSSQL = 'MSSQL',
}

export interface DatasourceConfiguration {
  // 数据库连接配置
  host?: string;
  port?: number;
  database?: string;
  username?: string;
  password?: string;
  
  // SSL配置
  ssl?: {
    enabled: boolean;
    cert?: string;
    key?: string;
    ca?: string;
  };
  
  // 连接池配置
  connectionPool?: {
    minSize?: number;
    maxSize?: number;
    timeout?: number;
  };
  
  // API配置
  url?: string;
  headers?: Record<string, string>;
  authentication?: {
    type: 'none' | 'basic' | 'bearer' | 'api-key' | 'oauth2';
    username?: string;
    password?: string;
    token?: string;
    apiKey?: string;
    apiKeyHeader?: string;
  };
  
  // 其他配置
  timeout?: number;
  retryCount?: number;
  customConfig?: Record<string, any>;
}

export interface Datasource {
  id: string;
  name: string;
  type: DatasourceType;
  organizationId: string;
  configuration: DatasourceConfiguration;
  isValid: boolean;
  invalids: string[];
  createdAt: string;
  updatedAt: string;
  userPermissions: string[];
}

export interface DatasourceTestResult {
  isValid: boolean;
  error?: string;
  responseTime?: number;
  connectionDetails?: {
    host: string;
    port: number;
    database?: string;
    version?: string;
  };
}

export interface DatasourceStructure {
  tables?: DatabaseTable[];
  collections?: MongoCollection[];
  endpoints?: ApiEndpoint[];
  schemas?: GraphQLSchema[];
}

export interface DatabaseTable {
  name: string;
  type: 'table' | 'view';
  columns: DatabaseColumn[];
  primaryKeys: string[];
  foreignKeys: DatabaseForeignKey[];
  indexes: DatabaseIndex[];
}

export interface DatabaseColumn {
  name: string;
  type: string;
  nullable: boolean;
  defaultValue?: string;
  comment?: string;
  isPrimaryKey: boolean;
  isForeignKey: boolean;
  isUnique: boolean;
  isIndexed: boolean;
}

export interface DatabaseForeignKey {
  columnName: string;
  referencedTable: string;
  referencedColumn: string;
  constraintName: string;
}

export interface DatabaseIndex {
  name: string;
  columns: string[];
  isUnique: boolean;
  type: string;
}

export interface MongoCollection {
  name: string;
  documentCount: number;
  avgDocumentSize: number;
  indexes: MongoIndex[];
  sampleDocument?: Record<string, any>;
}

export interface MongoIndex {
  name: string;
  keys: Record<string, number>;
  isUnique: boolean;
  isSparse: boolean;
}

export interface ApiEndpoint {
  path: string;
  method: string;
  description?: string;
  parameters?: ApiParameter[];
  responses?: ApiResponse[];
}

export interface ApiParameter {
  name: string;
  type: string;
  required: boolean;
  description?: string;
  example?: any;
}

export interface ApiResponse {
  statusCode: number;
  description: string;
  schema?: any;
  example?: any;
}

export interface GraphQLSchema {
  types: GraphQLType[];
  queries: GraphQLField[];
  mutations: GraphQLField[];
  subscriptions: GraphQLField[];
}

export interface GraphQLType {
  name: string;
  kind: string;
  description?: string;
  fields: GraphQLField[];
}

export interface GraphQLField {
  name: string;
  type: string;
  description?: string;
  args: GraphQLArgument[];
}

export interface GraphQLArgument {
  name: string;
  type: string;
  description?: string;
  defaultValue?: any;
}
