import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import CloseSquareFilledSvg from "@ant-design/icons-svg/es/asn/CloseSquareFilled";
import AntdIcon from "../components/AntdIcon";
var CloseSquareFilled = function CloseSquareFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: CloseSquareFilledSvg
  }));
};

/**![close-square](data:image/svg+xml;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************) */
var RefIcon = /*#__PURE__*/React.forwardRef(CloseSquareFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CloseSquareFilled';
}
export default RefIcon;