/* TailwindCSS基础样式 */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式重置 */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  height: 100%;
}

/* 代码字体 */
code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 选择文本样式 */
::selection {
  background: rgba(24, 144, 255, 0.2);
}

/* 焦点样式 */
:focus-visible {
  outline: 2px solid #1890ff;
  outline-offset: 2px;
}

/* Ant Design样式覆盖 */
.ant-layout {
  background: transparent;
}

.ant-menu {
  background: transparent;
}

.ant-menu-item {
  border-radius: 6px !important;
}

.ant-menu-item-selected {
  background-color: #1890ff !important;
  color: white !important;
}

.ant-menu-item-selected .ant-menu-item-icon {
  color: white !important;
}

/* Monaco Editor样式 */
.monaco-editor {
  border-radius: 6px;
}

.monaco-editor .margin {
  background-color: transparent;
}

/* 自定义工具类 */
.tw-text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tw-break-words {
  word-break: break-word;
}

.tw-scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

.tw-scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.tw-scrollbar-thin::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.tw-scrollbar-thin::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

/* 动画类 */
.tw-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.tw-slide-up {
  animation: slideUp 0.3s ease-out;
}

.tw-scale-in {
  animation: scaleIn 0.2s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    transform: translateY(20px); 
    opacity: 0; 
  }
  to { 
    transform: translateY(0); 
    opacity: 1; 
  }
}

@keyframes scaleIn {
  from { 
    transform: scale(0.95); 
    opacity: 0; 
  }
  to { 
    transform: scale(1); 
    opacity: 1; 
  }
}

/* 响应式断点 */
@media (max-width: 768px) {
  .tw-mobile-hidden {
    display: none !important;
  }
}

@media (min-width: 769px) {
  .tw-desktop-hidden {
    display: none !important;
  }
}

/* 打印样式 */
@media print {
  .tw-no-print {
    display: none !important;
  }
  
  .tw-print-break {
    page-break-after: always;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  :root {
    --dataset-border: #000000;
    --dataset-text: #000000;
    --dataset-bg: #ffffff;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
