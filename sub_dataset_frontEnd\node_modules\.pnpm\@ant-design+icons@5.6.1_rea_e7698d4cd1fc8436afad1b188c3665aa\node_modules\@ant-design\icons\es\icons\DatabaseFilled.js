import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import DatabaseFilledSvg from "@ant-design/icons-svg/es/asn/DatabaseFilled";
import AntdIcon from "../components/AntdIcon";
var DatabaseFilled = function DatabaseFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: DatabaseFilledSvg
  }));
};

/**![database](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgzMiA2NEgxOTJjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjIyNGg3MDRWOTZjMC0xNy43LTE0LjMtMzItMzItMzJ6TTI4OCAyMzJjLTIyLjEgMC00MC0xNy45LTQwLTQwczE3LjktNDAgNDAtNDAgNDAgMTcuOSA0MCA0MC0xNy45IDQwLTQwIDQwek0xNjAgOTI4YzAgMTcuNyAxNC4zIDMyIDMyIDMyaDY0MGMxNy43IDAgMzItMTQuMyAzMi0zMlY3MDRIMTYwdjIyNHptMTI4LTEzNmMyMi4xIDAgNDAgMTcuOSA0MCA0MHMtMTcuOSA0MC00MCA0MC00MC0xNy45LTQwLTQwIDE3LjktNDAgNDAtNDB6TTE2MCA2NDBoNzA0VjM4NEgxNjB2MjU2em0xMjgtMTY4YzIyLjEgMCA0MCAxNy45IDQwIDQwcy0xNy45IDQwLTQwIDQwLTQwLTE3LjktNDAtNDAgMTcuOS00MCA0MC00MHoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(DatabaseFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'DatabaseFilled';
}
export default RefIcon;