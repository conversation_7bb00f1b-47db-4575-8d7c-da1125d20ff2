import React from 'react';
import { Card, Typography, Button } from 'antd';
import { DatabaseOutlined } from '@ant-design/icons';
import styled from 'styled-components';

const { Title, Paragraph } = Typography;

const BuilderContainer = styled(Card)`
  text-align: center;
  max-width: 600px;
  margin: 60px auto;
  
  .builder-icon {
    font-size: 64px;
    color: var(--dataset-primary);
    margin-bottom: 24px;
  }
  
  .builder-title {
    margin-bottom: 16px;
  }
  
  .builder-description {
    color: var(--dataset-text-secondary);
    margin-bottom: 32px;
  }
`;

interface QueryBuilderProps {
  datasourceId?: string;
  onSave?: (query: any) => void;
  onCancel?: () => void;
}

const QueryBuilder: React.FC<QueryBuilderProps> = ({ datasourceId, onSave, onCancel }) => {
  return (
    <BuilderContainer>
      <DatabaseOutlined className="builder-icon" />
      <Title level={2} className="builder-title">
        可视化查询构建器
      </Title>
      <Paragraph className="builder-description">
        可视化查询构建器组件正在开发中，敬请期待！
        <br />
        您将能够通过拖拽的方式构建复杂的数据库查询，无需编写代码。
      </Paragraph>
      <Button type="primary" disabled>
        开始构建查询（即将推出）
      </Button>
    </BuilderContainer>
  );
};

export default QueryBuilder;
