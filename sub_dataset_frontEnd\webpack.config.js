// webpack.config.js - 数据集微前端应用配置
const ModuleFederationPlugin = require('webpack/lib/container/ModuleFederationPlugin')
const HtmlWebpackPlugin = require('html-webpack-plugin')
const path = require('path')

module.exports = (env, argv) => {
  const isProduction = argv.mode === 'production'

  return {
    mode: isProduction ? 'production' : 'development',
    entry: './src/index.tsx',

    devServer: {
      port: 3002,
      hot: true,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
        'Access-Control-Allow-Headers': 'X-Requested-With, content-type, Authorization',
      },
      historyApiFallback: true,
      open: false,
    },
    
    resolve: {
      extensions: ['.tsx', '.ts', '.js', '.jsx'],
      alias: {
        '@': path.resolve(__dirname, 'src'),
      },
    },
    
    module: {
      rules: [
        {
          test: /\.tsx?$/,
          use: [
            {
              loader: 'ts-loader',
              options: {
                transpileOnly: true,
              },
            },
          ],
          exclude: /node_modules/,
        },
        {
          test: /\.css$/,
          use: [
            'style-loader',
            'css-loader',
            {
              loader: 'postcss-loader',
              options: {
                postcssOptions: {
                  plugins: [
                    require('tailwindcss'),
                    require('autoprefixer'),
                  ],
                },
              },
            },
          ],
        },
        {
          test: /\.(png|jpe?g|gif|svg)$/i,
          type: 'asset/resource',
        },
      ],
    },
    
    plugins: [
      new ModuleFederationPlugin({
        name: 'datasetApp',
        filename: 'remoteEntry.js',
        exposes: {
          './DatasetApp': './src/App',
          './DatasetRoutes': './src/routes',
          './DatasetStore': './src/store',
          './APIEditor': './src/components/APIEditor',
          './QueryEditor': './src/components/QueryEditor',
          './JSEditor': './src/components/JSEditor',
          './QueryBuilder': './src/components/QueryBuilder',
          './CollectionManager': './src/components/CollectionManager'
        },
        shared: {
          react: {
            singleton: true,
            requiredVersion: '^18.2.0',
            eager: false,
          },
          'react-dom': {
            singleton: true,
            requiredVersion: '^18.2.0',
            eager: false,
          },
          'react-router-dom': {
            singleton: true,
            requiredVersion: '^6.0.0',
          },
          antd: {
            singleton: true,
            requiredVersion: '^5.0.0',
          },
          'styled-components': {
            singleton: true,
            requiredVersion: '^6.0.0',
          },
          '@reduxjs/toolkit': {
            singleton: true,
            requiredVersion: '^1.9.0',
          },
          'react-redux': {
            singleton: true,
            requiredVersion: '^8.0.0',
          },
        },
      }),
      
      new HtmlWebpackPlugin({
        template: './public/index.html',
        title: 'PagePlug 数据集管理',
        favicon: './public/favicon.ico',
      }),
    ],
    
    optimization: {
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
          },
          antd: {
            test: /[\\/]node_modules[\\/]antd[\\/]/,
            name: 'antd',
            chunks: 'all',
          },
          styledComponents: {
            test: /[\\/]node_modules[\\/]styled-components[\\/]/,
            name: 'styled-components',
            chunks: 'all',
          },
        },
      },
    },
    
    devtool: isProduction ? 'source-map' : 'eval-source-map',
    
    output: {
      path: path.resolve(__dirname, 'dist'),
      filename: isProduction ? '[name].[contenthash].js' : '[name].js',
      chunkFilename: isProduction ? '[name].[contenthash].chunk.js' : '[name].chunk.js',
      publicPath: isProduction ? '/dataset-app/' : 'http://localhost:3002/',
      clean: true,
    },
  }
}
