import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import DribbbleOutlinedSvg from "@ant-design/icons-svg/es/asn/DribbbleOutlined";
import AntdIcon from "../components/AntdIcon";
var DribbbleOutlined = function DribbbleOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: DribbbleOutlinedSvg
  }));
};

/**![dribbble](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA5NkMyODIuNiA5NiA5NiAyODIuNiA5NiA1MTJzMTg2LjYgNDE2IDQxNiA0MTYgNDE2LTE4Ni42IDQxNi00MTZTNzQxLjQgOTYgNTEyIDk2em0yNzUuMSAxOTEuOGM0OS41IDYwLjUgNzkuNSAxMzcuNSA4MC4yIDIyMS40LTExLjctMi41LTEyOS4yLTI2LjMtMjQ3LjQtMTEuNC0yLjUtNi4xLTUtMTIuMi03LjYtMTguMy03LjQtMTcuMy0xNS4zLTM0LjYtMjMuNi01MS41QzcyMCAzNzQuMyA3NzkuNiAyOTggNzg3LjEgMjg3Ljh6TTUxMiAxNTcuMmM5MC4zIDAgMTcyLjggMzMuOSAyMzUuNSA4OS41LTYuNCA5LjEtNTkuOSA4MS0xODYuMiAxMjguNC01OC4yLTEwNy0xMjIuNy0xOTQuOC0xMzIuNi0yMDggMjcuMy02LjYgNTUuMi05LjkgODMuMy05Ljl6TTM2MC45IDE5MWM5LjQgMTIuOCA3Mi45IDEwMC45IDEzMS43IDIwNS41QzMyNi40IDQ0MC42IDE4MCA0NDAgMTY0LjEgNDM5LjhjMjMuMS0xMTAuMyA5Ny40LTIwMS45IDE5Ni44LTI0OC44ek0xNTYuNyA1MTIuNWMwLTMuNi4xLTcuMy4yLTEwLjkgMTUuNS4zIDE4Ny43IDIuNSAzNjUuMi01MC42IDEwLjIgMTkuOSAxOS45IDQwLjEgMjguOCA2MC4zLTQuNyAxLjMtOS40IDIuNy0xNCA0LjJDMzUzLjYgNTc0LjkgMjU2LjEgNzM2LjQgMjQ4IDc1MC4xYy01Ni43LTYzLTkxLjMtMTQ2LjMtOTEuMy0yMzcuNnpNNTEyIDg2Ny44Yy04Mi4yIDAtMTU3LjktMjgtMjE4LjEtNzUgNi40LTEzLjEgNzguMy0xNTIgMjc4LjctMjIxLjlsMi4zLS44YzQ5LjkgMTI5LjYgNzAuNSAyMzguMyA3NS44IDI2OS41QTM1MC40NiAzNTAuNDYgMCAwMTUxMiA4NjcuOHptMTk4LjUtNjAuN2MtMy42LTIxLjYtMjIuNS0xMjUuNi02OS0yNTMuM0M3NTIuOSA1MzYgODUwLjcgNTY1LjIgODYyLjggNTY5Yy0xNS44IDk4LjgtNzIuNSAxODQuMi0xNTIuMyAyMzguMXoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(DribbbleOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'DribbbleOutlined';
}
export default RefIcon;