import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import CreditCardTwoToneSvg from "@ant-design/icons-svg/es/asn/CreditCardTwoTone";
import AntdIcon from "../components/AntdIcon";
var CreditCardTwoTone = function CreditCardTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: CreditCardTwoToneSvg
  }));
};

/**![credit-card](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEzNiA3OTJoNzUyVjQ0MEgxMzZ2MzUyem01MDctMTQ0YzAtNC40IDMuNi04IDgtOGgxNjVjNC40IDAgOCAzLjYgOCA4djcyYzAgNC40LTMuNiA4LTggOEg2NTFjLTQuNCAwLTgtMy42LTgtOHYtNzJ6TTEzNiAyMzJoNzUydjEyMEgxMzZ6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik02NTEgNzI4aDE2NWM0LjQgMCA4LTMuNiA4LTh2LTcyYzAtNC40LTMuNi04LTgtOEg2NTFjLTQuNCAwLTggMy42LTggOHY3MmMwIDQuNCAzLjYgOCA4IDh6IiBmaWxsPSIjMTY3N2ZmIiAvPjxwYXRoIGQ9Ik05MjggMTYwSDk2Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY2NDBjMCAxNy43IDE0LjMgMzIgMzIgMzJoODMyYzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE5MmMwLTE3LjctMTQuMy0zMi0zMi0zMnptLTQwIDYzMkgxMzZWNDQwaDc1MnYzNTJ6bTAtNDQwSDEzNlYyMzJoNzUydjEyMHoiIGZpbGw9IiMxNjc3ZmYiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(CreditCardTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CreditCardTwoTone';
}
export default RefIcon;