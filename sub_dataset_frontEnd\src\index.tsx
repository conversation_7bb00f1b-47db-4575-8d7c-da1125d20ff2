import React from 'react';
import { createRoot } from 'react-dom/client';

import App from './App';
import './styles/index.css';

// 获取根元素
const container = document.getElementById('root');
if (!container) {
  throw new Error('Root element not found');
}

// 创建React根实例
const root = createRoot(container);

// 渲染应用
root.render(<App />);

// 热模块替换支持
if (module.hot) {
  module.hot.accept('./App', () => {
    const NextApp = require('./App').default;
    root.render(<NextApp />);
  });
}

// 性能监控
if (process.env.NODE_ENV === 'production') {
  // 在生产环境中启用性能监控
  import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
    getCLS(console.log);
    getFID(console.log);
    getFCP(console.log);
    getLCP(console.log);
    getTTFB(console.log);
  });
}

// 错误监控
window.addEventListener('error', (event) => {
  console.error('Global error:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason);
});

// 导出用于微前端的生命周期函数
export { mount, unmount } from './App';
