#!/bin/bash

echo "========================================"
echo "PagePlug 数据集管理微前端应用启动脚本"
echo "========================================"
echo

echo "检查 Node.js 版本..."
if ! command -v node &> /dev/null; then
    echo "错误: 未找到 Node.js，请先安装 Node.js 16.x 或更高版本"
    exit 1
fi
node --version

echo
echo "检查 npm 版本..."
if ! command -v npm &> /dev/null; then
    echo "错误: 未找到 npm"
    exit 1
fi
npm --version

echo
echo "检查是否存在 node_modules 目录..."
if [ ! -d "node_modules" ]; then
    echo "node_modules 目录不存在，开始安装依赖..."
    echo
    echo "正在安装依赖，请稍候..."
    npm install
    if [ $? -ne 0 ]; then
        echo
        echo "依赖安装失败！请查看 INSTALL.md 文件获取解决方案"
        exit 1
    fi
    echo "依赖安装完成！"
else
    echo "node_modules 目录已存在，跳过依赖安装"
fi

echo
echo "启动开发服务器..."
echo "应用将在 http://localhost:3002 启动"
echo "按 Ctrl+C 停止服务器"
echo

npm run dev
