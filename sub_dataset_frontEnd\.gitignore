# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production build
dist/
build/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
logs
*.log

# Coverage
coverage/
.nyc_output

# Cache
.cache/
.parcel-cache/

# Temporary files
.tmp/
.temp/

# Lock files (choose one)
package-lock.json
# yarn.lock

# TypeScript
*.tsbuildinfo

# ESLint cache
.eslintcache

# Prettier cache
.prettiercache

# Husky
.husky/_
