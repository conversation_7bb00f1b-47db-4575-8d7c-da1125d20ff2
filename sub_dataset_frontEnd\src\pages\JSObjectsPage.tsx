import React from 'react';
import { Card, Typography, Button } from 'antd';
import { CodeOutlined, PlusOutlined } from '@ant-design/icons';
import styled from 'styled-components';

import { PageContainer } from '@/styles/global';

const { Title, Paragraph } = Typography;

const ComingSoonCard = styled(Card)`
  text-align: center;
  max-width: 600px;
  margin: 60px auto;
  
  .coming-soon-icon {
    font-size: 64px;
    color: var(--dataset-warning);
    margin-bottom: 24px;
  }
  
  .coming-soon-title {
    margin-bottom: 16px;
  }
  
  .coming-soon-description {
    color: var(--dataset-text-secondary);
    margin-bottom: 32px;
  }
`;

const JSObjectsPage: React.FC = () => {
  return (
    <PageContainer>
      <ComingSoonCard>
        <CodeOutlined className="coming-soon-icon" />
        <Title level={2} className="coming-soon-title">
          JS对象管理
        </Title>
        <Paragraph className="coming-soon-description">
          JavaScript对象管理功能正在开发中，敬请期待！
          <br />
          您将能够创建和管理可重用的JavaScript函数和对象，用于数据处理和业务逻辑。
        </Paragraph>
        <Button type="primary" icon={<PlusOutlined />} disabled>
          创建JS对象（即将推出）
        </Button>
      </ComingSoonCard>
    </PageContainer>
  );
};

export default JSObjectsPage;
