// 数据集Collection相关类型定义

import { Action, ActionResult } from './action';

export interface Collection {
  id: string;
  name: string;
  description?: string;
  applicationId: string;
  actions: string[]; // Action IDs
  variables: CollectionVariable[];
  createdAt: string;
  updatedAt: string;
  userPermissions: string[];
}

export interface CollectionVariable {
  key: string;
  value: string;
  type: 'string' | 'number' | 'boolean' | 'object';
  description?: string;
  isSecret?: boolean;
}

export interface CollectionExecutionResult {
  collectionId: string;
  results: Record<string, ActionResult>;
  variables: Record<string, any>;
  isSuccess: boolean;
  duration: string;
  executedAt: string;
  errors: CollectionExecutionError[];
}

export interface CollectionExecutionError {
  actionId: string;
  actionName: string;
  error: string;
  errorType: string;
}

export interface CollectionCreateRequest {
  name: string;
  description?: string;
  applicationId: string;
  actions?: string[];
  variables?: CollectionVariable[];
}

export interface CollectionUpdateRequest {
  name?: string;
  description?: string;
  actions?: string[];
  variables?: CollectionVariable[];
}

export interface CollectionExecutionRequest {
  collectionId: string;
  variables?: Record<string, any>;
  context?: {
    params?: Record<string, any>;
    globals?: Record<string, any>;
  };
}

export interface CollectionActionItem {
  action: Action;
  order: number;
  enabled: boolean;
  continueOnError: boolean;
  delay?: number; // 延迟执行时间（毫秒）
}

export interface CollectionTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  actions: Partial<Action>[];
  variables: CollectionVariable[];
  tags: string[];
  isPublic: boolean;
  createdBy: string;
  createdAt: string;
}

export interface CollectionImportRequest {
  templateId?: string;
  data?: {
    collection: Partial<Collection>;
    actions: Partial<Action>[];
  };
  applicationId: string;
}

export interface CollectionExportData {
  collection: Collection;
  actions: Action[];
  metadata: {
    exportedAt: string;
    version: string;
    source: string;
  };
}
