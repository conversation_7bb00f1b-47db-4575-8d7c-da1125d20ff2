import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

import { ActionResult, ActionExecutionRequest } from '@/types/action';
import { CollectionExecutionResult, CollectionExecutionRequest } from '@/types/collection';
import { actionAPI } from '@/services/api/actionAPI';
import { collectionAPI } from '@/services/api/collectionAPI';

// 异步Actions
export const executeAction = createAsyncThunk(
  'execution/executeAction',
  async (request: ActionExecutionRequest) => {
    const response = await actionAPI.executeAction(request);
    if (!response.success) {
      throw new Error(response.message || 'Failed to execute action');
    }
    return { actionId: request.actionId, result: response.data };
  }
);

export const executeCollection = createAsyncThunk(
  'execution/executeCollection',
  async (request: CollectionExecutionRequest) => {
    const response = await collectionAPI.executeCollection(request);
    if (!response.success) {
      throw new Error(response.message || 'Failed to execute collection');
    }
    return response.data;
  }
);

// State接口
interface ExecutionState {
  // Action执行状态
  executingActions: string[];
  results: Record<string, ActionResult>;
  errors: Record<string, string>;
  
  // Collection执行状态
  executingCollections: string[];
  collectionResults: Record<string, CollectionExecutionResult>;
  collectionErrors: Record<string, string>;
  
  // 执行历史
  executionHistory: Array<{
    id: string;
    type: 'action' | 'collection';
    targetId: string;
    targetName: string;
    startTime: string;
    endTime?: string;
    duration?: string;
    status: 'running' | 'success' | 'error';
    error?: string;
  }>;
  
  // 执行上下文
  executionContext: {
    params: Record<string, any>;
    globals: Record<string, any>;
    variables: Record<string, any>;
  };
  
  // 调试信息
  debugMode: boolean;
  debugLogs: Array<{
    id: string;
    timestamp: string;
    level: 'info' | 'warn' | 'error' | 'debug';
    message: string;
    data?: any;
  }>;
}

const initialState: ExecutionState = {
  executingActions: [],
  results: {},
  errors: {},
  executingCollections: [],
  collectionResults: {},
  collectionErrors: {},
  executionHistory: [],
  executionContext: {
    params: {},
    globals: {},
    variables: {},
  },
  debugMode: false,
  debugLogs: [],
};

const executionSlice = createSlice({
  name: 'execution',
  initialState,
  reducers: {
    // 设置执行上下文
    setExecutionContext: (state, action: PayloadAction<Partial<ExecutionState['executionContext']>>) => {
      state.executionContext = { ...state.executionContext, ...action.payload };
    },
    
    // 更新参数
    updateParams: (state, action: PayloadAction<Record<string, any>>) => {
      state.executionContext.params = { ...state.executionContext.params, ...action.payload };
    },
    
    // 更新全局变量
    updateGlobals: (state, action: PayloadAction<Record<string, any>>) => {
      state.executionContext.globals = { ...state.executionContext.globals, ...action.payload };
    },
    
    // 更新变量
    updateVariables: (state, action: PayloadAction<Record<string, any>>) => {
      state.executionContext.variables = { ...state.executionContext.variables, ...action.payload };
    },
    
    // 清除执行结果
    clearActionResult: (state, action: PayloadAction<string>) => {
      const actionId = action.payload;
      delete state.results[actionId];
      delete state.errors[actionId];
    },
    
    clearCollectionResult: (state, action: PayloadAction<string>) => {
      const collectionId = action.payload;
      delete state.collectionResults[collectionId];
      delete state.collectionErrors[collectionId];
    },
    
    clearAllResults: (state) => {
      state.results = {};
      state.errors = {};
      state.collectionResults = {};
      state.collectionErrors = {};
    },
    
    // 调试模式
    setDebugMode: (state, action: PayloadAction<boolean>) => {
      state.debugMode = action.payload;
    },
    
    // 添加调试日志
    addDebugLog: (state, action: PayloadAction<{
      level: 'info' | 'warn' | 'error' | 'debug';
      message: string;
      data?: any;
    }>) => {
      const log = {
        id: `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date().toISOString(),
        ...action.payload,
      };
      
      state.debugLogs.unshift(log);
      
      // 限制日志数量
      if (state.debugLogs.length > 1000) {
        state.debugLogs = state.debugLogs.slice(0, 1000);
      }
    },
    
    // 清除调试日志
    clearDebugLogs: (state) => {
      state.debugLogs = [];
    },
    
    // 清除执行历史
    clearExecutionHistory: (state) => {
      state.executionHistory = [];
    },
    
    // 停止执行
    stopActionExecution: (state, action: PayloadAction<string>) => {
      const actionId = action.payload;
      state.executingActions = state.executingActions.filter(id => id !== actionId);
      
      // 更新执行历史
      const historyItem = state.executionHistory.find(
        item => item.targetId === actionId && item.status === 'running'
      );
      if (historyItem) {
        historyItem.status = 'error';
        historyItem.endTime = new Date().toISOString();
        historyItem.error = 'Execution stopped by user';
      }
    },
    
    stopCollectionExecution: (state, action: PayloadAction<string>) => {
      const collectionId = action.payload;
      state.executingCollections = state.executingCollections.filter(id => id !== collectionId);
      
      // 更新执行历史
      const historyItem = state.executionHistory.find(
        item => item.targetId === collectionId && item.status === 'running'
      );
      if (historyItem) {
        historyItem.status = 'error';
        historyItem.endTime = new Date().toISOString();
        historyItem.error = 'Execution stopped by user';
      }
    },
  },
  
  extraReducers: (builder) => {
    builder
      // executeAction
      .addCase(executeAction.pending, (state, action) => {
        const actionId = action.meta.arg.actionId;
        
        if (!state.executingActions.includes(actionId)) {
          state.executingActions.push(actionId);
        }
        
        // 清除之前的错误
        delete state.errors[actionId];
        
        // 添加到执行历史
        state.executionHistory.unshift({
          id: `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          type: 'action',
          targetId: actionId,
          targetName: `Action ${actionId}`, // 实际应用中应该从store获取action名称
          startTime: new Date().toISOString(),
          status: 'running',
        });
      })
      .addCase(executeAction.fulfilled, (state, action) => {
        const { actionId, result } = action.payload;
        
        // 移除执行状态
        state.executingActions = state.executingActions.filter(id => id !== actionId);
        
        // 保存结果
        state.results[actionId] = result;
        
        // 更新执行历史
        const historyItem = state.executionHistory.find(
          item => item.targetId === actionId && item.status === 'running'
        );
        if (historyItem) {
          historyItem.status = 'success';
          historyItem.endTime = new Date().toISOString();
          historyItem.duration = result.duration;
        }
        
        // 添加调试日志
        if (state.debugMode) {
          state.debugLogs.unshift({
            id: `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            timestamp: new Date().toISOString(),
            level: 'info',
            message: `Action ${actionId} executed successfully`,
            data: result,
          });
        }
      })
      .addCase(executeAction.rejected, (state, action) => {
        const actionId = action.meta.arg.actionId;
        
        // 移除执行状态
        state.executingActions = state.executingActions.filter(id => id !== actionId);
        
        // 保存错误
        state.errors[actionId] = action.error.message || 'Execution failed';
        
        // 更新执行历史
        const historyItem = state.executionHistory.find(
          item => item.targetId === actionId && item.status === 'running'
        );
        if (historyItem) {
          historyItem.status = 'error';
          historyItem.endTime = new Date().toISOString();
          historyItem.error = action.error.message;
        }
        
        // 添加调试日志
        if (state.debugMode) {
          state.debugLogs.unshift({
            id: `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            timestamp: new Date().toISOString(),
            level: 'error',
            message: `Action ${actionId} execution failed`,
            data: action.error,
          });
        }
      })
      
      // executeCollection
      .addCase(executeCollection.pending, (state, action) => {
        const collectionId = action.meta.arg.collectionId;
        
        if (!state.executingCollections.includes(collectionId)) {
          state.executingCollections.push(collectionId);
        }
        
        // 清除之前的错误
        delete state.collectionErrors[collectionId];
        
        // 添加到执行历史
        state.executionHistory.unshift({
          id: `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          type: 'collection',
          targetId: collectionId,
          targetName: `Collection ${collectionId}`,
          startTime: new Date().toISOString(),
          status: 'running',
        });
      })
      .addCase(executeCollection.fulfilled, (state, action) => {
        const result = action.payload;
        const collectionId = result.collectionId;
        
        // 移除执行状态
        state.executingCollections = state.executingCollections.filter(id => id !== collectionId);
        
        // 保存结果
        state.collectionResults[collectionId] = result;
        
        // 更新执行历史
        const historyItem = state.executionHistory.find(
          item => item.targetId === collectionId && item.status === 'running'
        );
        if (historyItem) {
          historyItem.status = result.isSuccess ? 'success' : 'error';
          historyItem.endTime = new Date().toISOString();
          historyItem.duration = result.duration;
          if (!result.isSuccess && result.errors.length > 0) {
            historyItem.error = result.errors[0].error;
          }
        }
        
        // 添加调试日志
        if (state.debugMode) {
          state.debugLogs.unshift({
            id: `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            timestamp: new Date().toISOString(),
            level: result.isSuccess ? 'info' : 'error',
            message: `Collection ${collectionId} execution ${result.isSuccess ? 'completed' : 'failed'}`,
            data: result,
          });
        }
      })
      .addCase(executeCollection.rejected, (state, action) => {
        const collectionId = action.meta.arg.collectionId;
        
        // 移除执行状态
        state.executingCollections = state.executingCollections.filter(id => id !== collectionId);
        
        // 保存错误
        state.collectionErrors[collectionId] = action.error.message || 'Execution failed';
        
        // 更新执行历史
        const historyItem = state.executionHistory.find(
          item => item.targetId === collectionId && item.status === 'running'
        );
        if (historyItem) {
          historyItem.status = 'error';
          historyItem.endTime = new Date().toISOString();
          historyItem.error = action.error.message;
        }
        
        // 添加调试日志
        if (state.debugMode) {
          state.debugLogs.unshift({
            id: `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            timestamp: new Date().toISOString(),
            level: 'error',
            message: `Collection ${collectionId} execution failed`,
            data: action.error,
          });
        }
      });
  },
});

export const {
  setExecutionContext,
  updateParams,
  updateGlobals,
  updateVariables,
  clearActionResult,
  clearCollectionResult,
  clearAllResults,
  setDebugMode,
  addDebugLog,
  clearDebugLogs,
  clearExecutionHistory,
  stopActionExecution,
  stopCollectionExecution,
} = executionSlice.actions;

export { executionSlice };
