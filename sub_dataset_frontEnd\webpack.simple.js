// webpack.simple.js - 最简单的配置，用于快速测试
const HtmlWebpackPlugin = require('html-webpack-plugin')
const path = require('path')

module.exports = {
  mode: 'development',
  entry: './src/simple-bootstrap.tsx',
  
  devServer: {
    port: 3002,
    hot: true,
    historyApiFallback: true,
    open: false,
    static: {
      directory: path.join(__dirname, 'public'),
    },
  },
  
  resolve: {
    extensions: ['.tsx', '.ts', '.js', '.jsx'],
    alias: {
      '@': path.resolve(__dirname, 'src'),
    },
  },
  
  module: {
    rules: [
      {
        test: /\.(ts|tsx)$/,
        use: {
          loader: 'ts-loader',
          options: {
            transpileOnly: true, // 跳过类型检查，加快编译速度
          },
        },
        exclude: /node_modules/,
      },
      {
        test: /\.css$/,
        use: ['style-loader', 'css-loader'],
      },
      {
        test: /\.(png|jpe?g|gif|svg)$/i,
        type: 'asset/resource',
      },
    ],
  },
  
  plugins: [
    new HtmlWebpackPlugin({
      template: './public/index.html',
      title: 'PagePlug 数据集管理 - 测试模式',
    }),
  ],
  
  devtool: 'eval-source-map',
}
