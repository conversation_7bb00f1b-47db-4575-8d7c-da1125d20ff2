import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import AlipaySquareFilledSvg from "@ant-design/icons-svg/es/asn/AlipaySquareFilled";
import AntdIcon from "../components/AntdIcon";
var AlipaySquareFilled = function AlipaySquareFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: AlipaySquareFilledSvg
  }));
};

/**![alipay-square](data:image/svg+xml;base64,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) */
var RefIcon = /*#__PURE__*/React.forwardRef(AlipaySquareFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'AlipaySquareFilled';
}
export default RefIcon;