"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _FastBackwardOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/FastBackwardOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var FastBackwardOutlined = function FastBackwardOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _FastBackwardOutlined.default
  }));
};

/**![fast-backward](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjAgMCAxMDI0IDEwMjQiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxNy42IDI3My41TDIzMC4yIDQ5OS4zYTE2LjE0IDE2LjE0IDAgMDAwIDI1LjRsMjg3LjQgMjI1LjhjMTAuNyA4LjQgMjYuNC44IDI2LjQtMTIuN1YyODYuMmMwLTEzLjUtMTUuNy0yMS4xLTI2LjQtMTIuN3ptMzIwIDBMNTUwLjIgNDk5LjNhMTYuMTQgMTYuMTQgMCAwMDAgMjUuNGwyODcuNCAyMjUuOGMxMC43IDguNCAyNi40LjggMjYuNC0xMi43VjI4Ni4yYzAtMTMuNS0xNS43LTIxLjEtMjYuNC0xMi43em0tNjIwLTI1LjVoLTUxLjJjLTMuNSAwLTYuNCAyLjctNi40IDZ2NTE2YzAgMy4zIDIuOSA2IDYuNCA2aDUxLjJjMy41IDAgNi40LTIuNyA2LjQtNlYyNTRjMC0zLjMtMi45LTYtNi40LTZ6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(FastBackwardOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FastBackwardOutlined';
}
var _default = exports.default = RefIcon;