import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Menu, Typography, Divider } from 'antd';
import {
  ApiOutlined,
  FolderOutlined,
  DatabaseOutlined,
  CodeOutlined,
  FileTextOutlined,
  SettingOutlined,
  PlusOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import styled from 'styled-components';

import { useAppSelector, useAppDispatch } from '@/store';
import { openModal, setSearchVisible } from '@/store/slices/uiSlice';

const { Title } = Typography;

// 样式组件
const SidebarContainer = styled.div`
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--dataset-bg);
`;

const LogoSection = styled.div<{ collapsed: boolean }>`
  padding: 16px;
  text-align: center;
  border-bottom: 1px solid var(--dataset-border);
  
  .logo-title {
    margin: 0;
    color: var(--dataset-text);
    font-size: ${props => props.collapsed ? '14px' : '16px'};
    font-weight: 600;
    transition: all 0.3s ease;
    white-space: nowrap;
    overflow: hidden;
  }
  
  .logo-subtitle {
    margin: 0;
    color: var(--dataset-text-secondary);
    font-size: 12px;
    margin-top: 4px;
    opacity: ${props => props.collapsed ? 0 : 1};
    transition: opacity 0.3s ease;
  }
`;

const MenuSection = styled.div`
  flex: 1;
  overflow-y: auto;
  
  .ant-menu {
    background: transparent;
    border: none;
    
    .ant-menu-item {
      margin: 4px 8px;
      border-radius: var(--dataset-border-radius);
      
      &:hover {
        background: var(--dataset-bg-secondary);
      }
      
      &.ant-menu-item-selected {
        background: var(--dataset-primary);
        color: white;
        
        .ant-menu-item-icon {
          color: white;
        }
      }
    }
    
    .ant-menu-submenu {
      .ant-menu-submenu-title {
        margin: 4px 8px;
        border-radius: var(--dataset-border-radius);
        
        &:hover {
          background: var(--dataset-bg-secondary);
        }
      }
    }
  }
`;

const QuickActions = styled.div<{ collapsed: boolean }>`
  padding: 16px 8px;
  border-top: 1px solid var(--dataset-border);
  
  .quick-action-btn {
    width: 100%;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: ${props => props.collapsed ? 'center' : 'flex-start'};
    padding: 8px 12px;
    border: 1px solid var(--dataset-border);
    border-radius: var(--dataset-border-radius);
    background: var(--dataset-bg);
    color: var(--dataset-text);
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      background: var(--dataset-bg-secondary);
      border-color: var(--dataset-primary);
    }
    
    .anticon {
      font-size: 16px;
      margin-right: ${props => props.collapsed ? 0 : '8px'};
    }
    
    .btn-text {
      font-size: 14px;
      opacity: ${props => props.collapsed ? 0 : 1};
      transition: opacity 0.3s ease;
    }
  }
`;

// 菜单项配置
const menuItems = [
  {
    key: '/actions',
    icon: <ApiOutlined />,
    label: 'API管理',
    children: [
      { key: '/actions', label: 'API列表' },
      { key: '/actions/rest', label: 'REST API' },
      { key: '/actions/graphql', label: 'GraphQL' },
    ],
  },
  {
    key: '/collections',
    icon: <FolderOutlined />,
    label: '数据集合',
  },
  {
    key: '/query-builder',
    icon: <DatabaseOutlined />,
    label: '查询构建器',
    children: [
      { key: '/query-builder', label: '可视化构建' },
      { key: '/query-builder/sql', label: 'SQL编辑器' },
      { key: '/query-builder/nosql', label: 'NoSQL编辑器' },
    ],
  },
  {
    key: '/js-objects',
    icon: <CodeOutlined />,
    label: 'JS对象',
  },
  {
    key: '/templates',
    icon: <FileTextOutlined />,
    label: '查询模板',
  },
  {
    key: '/settings',
    icon: <SettingOutlined />,
    label: '设置',
  },
];

// 侧边栏组件
const Sidebar: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();
  
  const { sidebarCollapsed } = useAppSelector(state => state.ui);

  // 处理菜单点击
  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  // 快速操作处理
  const handleQuickAction = (action: string) => {
    switch (action) {
      case 'create-action':
        dispatch(openModal('createAction'));
        break;
      case 'create-collection':
        dispatch(openModal('createCollection'));
        break;
      case 'search':
        dispatch(setSearchVisible(true));
        break;
      default:
        break;
    }
  };

  // 获取当前选中的菜单项
  const getSelectedKeys = () => {
    const path = location.pathname;
    
    // 精确匹配
    if (menuItems.some(item => item.key === path)) {
      return [path];
    }
    
    // 模糊匹配
    for (const item of menuItems) {
      if (path.startsWith(item.key)) {
        return [item.key];
      }
      
      if (item.children) {
        for (const child of item.children) {
          if (path.startsWith(child.key)) {
            return [child.key];
          }
        }
      }
    }
    
    return [];
  };

  // 获取展开的菜单项
  const getOpenKeys = () => {
    const path = location.pathname;
    const openKeys: string[] = [];
    
    for (const item of menuItems) {
      if (item.children && item.children.some(child => path.startsWith(child.key))) {
        openKeys.push(item.key);
      }
    }
    
    return openKeys;
  };

  return (
    <SidebarContainer>
      {/* Logo区域 */}
      <LogoSection collapsed={sidebarCollapsed}>
        <Title level={4} className="logo-title">
          {sidebarCollapsed ? 'DS' : '数据集'}
        </Title>
        {!sidebarCollapsed && (
          <div className="logo-subtitle">Dataset Manager</div>
        )}
      </LogoSection>

      {/* 菜单区域 */}
      <MenuSection>
        <Menu
          mode="inline"
          selectedKeys={getSelectedKeys()}
          defaultOpenKeys={getOpenKeys()}
          items={menuItems}
          onClick={handleMenuClick}
          inlineCollapsed={sidebarCollapsed}
        />
      </MenuSection>

      {/* 快速操作区域 */}
      <QuickActions collapsed={sidebarCollapsed}>
        <div
          className="quick-action-btn"
          onClick={() => handleQuickAction('create-action')}
          title={sidebarCollapsed ? '创建API' : ''}
        >
          <PlusOutlined />
          <span className="btn-text">创建API</span>
        </div>
        
        <div
          className="quick-action-btn"
          onClick={() => handleQuickAction('create-collection')}
          title={sidebarCollapsed ? '创建集合' : ''}
        >
          <FolderOutlined />
          <span className="btn-text">创建集合</span>
        </div>
        
        <div
          className="quick-action-btn"
          onClick={() => handleQuickAction('search')}
          title={sidebarCollapsed ? '搜索' : ''}
        >
          <SearchOutlined />
          <span className="btn-text">搜索</span>
        </div>
      </QuickActions>
    </SidebarContainer>
  );
};

export default Sidebar;
