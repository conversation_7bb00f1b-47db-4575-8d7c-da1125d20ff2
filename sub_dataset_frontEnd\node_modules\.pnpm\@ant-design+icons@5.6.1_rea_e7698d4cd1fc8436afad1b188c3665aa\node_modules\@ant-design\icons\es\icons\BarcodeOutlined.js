import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import BarcodeOutlinedSvg from "@ant-design/icons-svg/es/asn/BarcodeOutlined";
import AntdIcon from "../components/AntdIcon";
var BarcodeOutlined = function BarcodeOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: BarcodeOutlinedSvg
  }));
};

/**![barcode](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEyMCAxNjBINzJjLTQuNCAwLTggMy42LTggOHY2ODhjMCA0LjQgMy42IDggOCA4aDQ4YzQuNCAwIDgtMy42IDgtOFYxNjhjMC00LjQtMy42LTgtOC04em04MzMgMGgtNDhjLTQuNCAwLTggMy42LTggOHY2ODhjMCA0LjQgMy42IDggOCA4aDQ4YzQuNCAwIDgtMy42IDgtOFYxNjhjMC00LjQtMy42LTgtOC04ek0yMDAgNzM2aDExMmM0LjQgMCA4LTMuNiA4LThWMTY4YzAtNC40LTMuNi04LTgtOEgyMDBjLTQuNCAwLTggMy42LTggOHY1NjBjMCA0LjQgMy42IDggOCA4em0zMjEgMGg0OGM0LjQgMCA4LTMuNiA4LThWMTY4YzAtNC40LTMuNi04LTgtOGgtNDhjLTQuNCAwLTggMy42LTggOHY1NjBjMCA0LjQgMy42IDggOCA4em0xMjYgMGgxNzhjNC40IDAgOC0zLjYgOC04VjE2OGMwLTQuNC0zLjYtOC04LThINjQ3Yy00LjQgMC04IDMuNi04IDh2NTYwYzAgNC40IDMuNiA4IDggOHptLTI1NSAwaDQ4YzQuNCAwIDgtMy42IDgtOFYxNjhjMC00LjQtMy42LTgtOC04aC00OGMtNC40IDAtOCAzLjYtOCA4djU2MGMwIDQuNCAzLjYgOCA4IDh6bS03OSA2NEgyMDFjLTQuNCAwLTggMy42LTggOHY0OGMwIDQuNCAzLjYgOCA4IDhoMTEyYzQuNCAwIDgtMy42IDgtOHYtNDhjMC00LjQtMy42LTgtOC04em0yNTcgMGgtNDhjLTQuNCAwLTggMy42LTggOHY0OGMwIDQuNCAzLjYgOCA4IDhoNDhjNC40IDAgOC0zLjYgOC04di00OGMwLTQuNC0zLjYtOC04LTh6bTI1NiAwSDY0OGMtNC40IDAtOCAzLjYtOCA4djQ4YzAgNC40IDMuNiA4IDggOGgxNzhjNC40IDAgOC0zLjYgOC04di00OGMwLTQuNC0zLjYtOC04LTh6bS0zODUgMGgtNDhjLTQuNCAwLTggMy42LTggOHY0OGMwIDQuNCAzLjYgOCA4IDhoNDhjNC40IDAgOC0zLjYgOC04di00OGMwLTQuNC0zLjYtOC04LTh6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(BarcodeOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'BarcodeOutlined';
}
export default RefIcon;