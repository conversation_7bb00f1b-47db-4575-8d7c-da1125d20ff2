"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _FunnelPlotFilled = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/FunnelPlotFilled"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var FunnelPlotFilled = function FunnelPlotFilled(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _FunnelPlotFilled.default
  }));
};

/**![funnel-plot](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTMzNi43IDU4NmgzNTAuNmw4NC45LTE0OEgyNTEuOHptNTQzLjQtNDMySDE0My45Yy0yNC41IDAtMzkuOCAyNi43LTI3LjUgNDhMMjE1IDM3NGg1OTRsOTguNy0xNzJjMTIuMi0yMS4zLTMuMS00OC0yNy42LTQ4ek0zNDkgODM4YzAgMTcuNyAxNC4yIDMyIDMxLjggMzJoMjYyLjRjMTcuNiAwIDMxLjgtMTQuMyAzMS44LTMyVjY1MEgzNDl2MTg4eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(FunnelPlotFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FunnelPlotFilled';
}
var _default = exports.default = RefIcon;