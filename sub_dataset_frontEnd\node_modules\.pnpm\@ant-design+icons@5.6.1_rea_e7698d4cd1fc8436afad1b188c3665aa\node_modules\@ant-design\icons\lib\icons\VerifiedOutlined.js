"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _VerifiedOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/VerifiedOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var VerifiedOutlined = function VerifiedOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _VerifiedOutlined.default
  }));
};

/**![verified](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik00NDcuOCA1ODguOGwtNy4zLTMyLjVjLS4yLTEtLjYtMS45LTEuMS0yLjdhNy45NCA3Ljk0IDAgMDAtMTEuMS0yLjJMNDA1IDU2N1Y0MTFjMC00LjQtMy42LTgtOC04aC04MWMtNC40IDAtOCAzLjYtOCA4djM2YzAgNC40IDMuNiA4IDggOGgzN3YxOTIuNGE4IDggMCAwMDEyLjcgNi41bDc5LTU2LjhjMi42LTEuOSAzLjgtNS4xIDMuMS04LjN6bS01Ni43LTIxNi42bC4yLjJjMy4yIDMgOC4zIDIuOCAxMS4zLS41bDI0LjEtMjYuMmE4LjEgOC4xIDAgMDAtLjMtMTEuMmwtNTMuNy01Mi4xYTggOCAwIDAwLTExLjIuMWwtMjQuNyAyNC43Yy0zLjEgMy4xLTMuMSA4LjIuMSAxMS4zbDU0LjIgNTMuN3oiIC8+PHBhdGggZD0iTTg2Ni45IDE2OS45TDUyNy4xIDU0LjFDNTIzIDUyLjcgNTE3LjUgNTIgNTEyIDUycy0xMSAuNy0xNS4xIDIuMUwxNTcuMSAxNjkuOWMtOC4zIDIuOC0xNS4xIDEyLjQtMTUuMSAyMS4ydjQ4Mi40YzAgOC44IDUuNyAyMC40IDEyLjYgMjUuOUw0OTkuMyA5NjhjMy41IDIuNyA4IDQuMSAxMi42IDQuMXM5LjItMS40IDEyLjYtNC4xbDM0NC43LTI2OC42YzYuOS01LjQgMTIuNi0xNyAxMi42LTI1LjlWMTkxLjFjLjItOC44LTYuNi0xOC4zLTE0LjktMjEuMnpNODEwIDY1NC4zTDUxMiA4ODYuNSAyMTQgNjU0LjNWMjI2LjdsMjk4LTEwMS42IDI5OCAxMDEuNnY0MjcuNnoiIC8+PHBhdGggZD0iTTQ1MiAyOTd2MzZjMCA0LjQgMy42IDggOCA4aDEwOHYyNzRoLTM4VjQwNWMwLTQuNC0zLjYtOC04LThoLTM1Yy00LjQgMC04IDMuNi04IDh2MjEwaC0zMWMtNC40IDAtOCAzLjYtOCA4djM3YzAgNC40IDMuNiA4IDggOGgyNDRjNC40IDAgOC0zLjYgOC04di0zN2MwLTQuNC0zLjYtOC04LThoLTcyVjQ5M2g1OGM0LjQgMCA4LTMuNiA4LTh2LTM1YzAtNC40LTMuNi04LTgtOGgtNThWMzQxaDYzYzQuNCAwIDgtMy42IDgtOHYtMzZjMC00LjQtMy42LTgtOC04SDQ2MGMtNC40IDAtOCAzLjYtOCA4eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(VerifiedOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'VerifiedOutlined';
}
var _default = exports.default = RefIcon;