"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _FallOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/FallOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var FallOutlined = function FallOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _FallOutlined.default
  }));
};

/**![fall](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkyNS45IDgwNGwtMjQtMTk5LjJjLS44LTYuNi04LjktOS40LTEzLjYtNC43TDgyOSA2NTkuNSA1NTcuNyAzODguM2MtNi4zLTYuMi0xNi40LTYuMi0yMi42IDBMNDMzLjMgNDkwIDE1Ni42IDIxMy4zYTguMDMgOC4wMyAwIDAwLTExLjMgMGwtNDUgNDUuMmE4LjAzIDguMDMgMCAwMDAgMTEuM0w0MjIgNTkxLjdjNi4yIDYuMyAxNi40IDYuMyAyMi42IDBMNTQ2LjQgNDkwbDIyNi4xIDIyNi01OS4zIDU5LjNhOC4wMSA4LjAxIDAgMDA0LjcgMTMuNmwxOTkuMiAyNGM1LjEuNyA5LjUtMy43IDguOC04Ljl6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(FallOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FallOutlined';
}
var _default = exports.default = RefIcon;