import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import DislikeTwoToneSvg from "@ant-design/icons-svg/es/asn/DislikeTwoTone";
import AntdIcon from "../components/AntdIcon";
var DislikeTwoTone = function DislikeTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: DislikeTwoToneSvg
  }));
};

/**![dislike](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI3MyAxMDAuMXY0MjhoLjNsLS4zLTQyOHpNODIwLjQgNTI1bC0yMS45LTE5IDE0LTI1LjVhNTYuMiA1Ni4yIDAgMDA2LjktMjcuM2MwLTE2LjUtNy4xLTMyLjItMTkuNi00M2wtMjEuOS0xOSAxMy45LTI1LjRhNTYuMiA1Ni4yIDAgMDA2LjktMjcuM2MwLTE2LjUtNy4xLTMyLjItMTkuNi00M2wtMjEuOS0xOSAxMy45LTI1LjRhNTYuMiA1Ni4yIDAgMDA2LjktMjcuM2MwLTIyLjQtMTMuMi00Mi42LTMzLjYtNTEuOEgzNDV2MzQ1LjJjMTguNiA2Ny4yIDQ2LjQgMTY4IDgzLjUgMzAyLjVhNDQuMjggNDQuMjggMCAwMDQyLjIgMzIuM2M3LjUuMSAxNS0yLjIgMjEuMS02LjcgOS45LTcuNCAxNS4yLTE4LjYgMTQuNi0zMC41bC05LjYtMTk4LjRoMzE0LjRDODI5IDYwNS41IDg0MCA1ODcuMSA4NDAgNTY4YzAtMTYuNS03LjEtMzIuMi0xOS42LTQzeiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNMTEyIDEzMnYzNjRjMCAxNy43IDE0LjMgMzIgMzIgMzJoNjVWMTAwaC02NWMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ6bTc3My45IDM1OC4zYzMuNi0xMiA1LjQtMjQuNCA1LjQtMzcgMC0yOC4zLTkuMy01NS41LTI2LjEtNzcuNyAzLjYtMTIgNS40LTI0LjQgNS40LTM3IDAtMjguMy05LjMtNTUuNS0yNi4xLTc3LjcgMy42LTEyIDUuNC0yNC40IDUuNC0zNyAwLTUxLjYtMzAuNy05OC4xLTc4LjMtMTE4LjRhNjYuMSA2Ni4xIDAgMDAtMjYuNS01LjRIMjczbC4zIDQyOCA4NS44IDMxMC44QzM3Mi45IDg4OSA0MTguOSA5MjQgNDcwLjkgOTI0YzI5LjcgMCA1Ny40LTExLjggNzcuOS0zMy40IDIwLjUtMjEuNSAzMS00OS43IDI5LjUtNzkuNGwtNi0xMjIuOWgyMzkuOWMxMi4xIDAgMjMuOS0zLjIgMzQuMy05LjMgNDAuNC0yMy41IDY1LjUtNjYuMSA2NS41LTExMSAwLTI4LjMtOS4zLTU1LjUtMjYuMS03Ny43em0tNzQuNyAxMjYuMUg0OTYuOGw5LjYgMTk4LjRjLjYgMTEuOS00LjcgMjMuMS0xNC42IDMwLjUtNi4xIDQuNS0xMy42IDYuOC0yMS4xIDYuN2E0NC4yOCA0NC4yOCAwIDAxLTQyLjItMzIuM2MtMzcuMS0xMzQuNC02NC45LTIzNS4yLTgzLjUtMzAyLjVWMTcyaDM5OS40YTU2Ljg1IDU2Ljg1IDAgMDEzMy42IDUxLjhjMCA5LjctMi4zIDE4LjktNi45IDI3LjNsLTEzLjkgMjUuNCAyMS45IDE5YTU2Ljc2IDU2Ljc2IDAgMDExOS42IDQzYzAgOS43LTIuMyAxOC45LTYuOSAyNy4zbC0xMy45IDI1LjQgMjEuOSAxOWE1Ni43NiA1Ni43NiAwIDAxMTkuNiA0M2MwIDkuNy0yLjMgMTguOS02LjkgMjcuM2wtMTQgMjUuNSAyMS45IDE5YTU2Ljc2IDU2Ljc2IDAgMDExOS42IDQzYzAgMTkuMS0xMSAzNy41LTI4LjggNDguNHoiIGZpbGw9IiMxNjc3ZmYiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(DislikeTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'DislikeTwoTone';
}
export default RefIcon;