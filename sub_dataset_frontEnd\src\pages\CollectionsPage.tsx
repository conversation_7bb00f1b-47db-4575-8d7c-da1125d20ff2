import React, { useEffect, useState } from 'react';
import { 
  Card, 
  Button, 
  Input, 
  Space, 
  Tag, 
  Tooltip, 
  Row,
  Col,
  Typography,
  Dropdown,
  Modal,
  Empty
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  PlayCircleOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  MoreOutlined,
  FolderOutlined,
  ApiOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import styled from 'styled-components';

import { useAppDispatch, useAppSelector } from '@/store';
import { fetchCollections, deleteCollection, duplicateCollection } from '@/store/slices/collectionSlice';
import { openModal, addNotification } from '@/store/slices/uiSlice';
import { Collection } from '@/types/collection';
import { PageContainer } from '@/styles/global';
import LoadingSpinner from '@/components/shared/LoadingSpinner';

const { Title, Text, Paragraph } = Typography;
const { Search } = Input;

// 样式组件
const PageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 0 24px;
  
  .header-left {
    .page-title {
      margin: 0;
      color: var(--dataset-text);
    }
    
    .page-description {
      color: var(--dataset-text-secondary);
      margin-top: 4px;
    }
  }
  
  .header-right {
    display: flex;
    gap: 12px;
  }
`;

const FilterSection = styled.div`
  margin-bottom: 24px;
  padding: 0 24px;
`;

const CollectionGrid = styled.div`
  padding: 0 24px;
`;

const CollectionCard = styled(Card)`
  height: 100%;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: var(--dataset-shadow-hover);
    transform: translateY(-2px);
  }
  
  .ant-card-body {
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  
  .collection-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 12px;
    
    .collection-icon {
      font-size: 24px;
      color: var(--dataset-primary);
      margin-right: 12px;
    }
    
    .collection-title {
      flex: 1;
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--dataset-text);
    }
    
    .collection-actions {
      opacity: 0;
      transition: opacity 0.3s ease;
    }
  }
  
  &:hover .collection-actions {
    opacity: 1;
  }
  
  .collection-description {
    color: var(--dataset-text-secondary);
    margin-bottom: 16px;
    flex: 1;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .collection-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    
    .meta-item {
      display: flex;
      align-items: center;
      gap: 4px;
      color: var(--dataset-text-secondary);
      font-size: 12px;
    }
  }
  
  .collection-tags {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 60px 20px;
  
  .empty-icon {
    font-size: 64px;
    color: var(--dataset-text-disabled);
    margin-bottom: 16px;
  }
  
  .empty-title {
    font-size: 16px;
    color: var(--dataset-text);
    margin-bottom: 8px;
  }
  
  .empty-description {
    color: var(--dataset-text-secondary);
    margin-bottom: 24px;
  }
`;

// Collections页面组件
const CollectionsPage: React.FC<{ initialData?: any }> = ({ initialData }) => {
  const dispatch = useAppDispatch();
  const { 
    items: collections, 
    loading, 
    error, 
    pagination 
  } = useAppSelector(state => state.collections);

  const [searchText, setSearchText] = useState('');

  // 加载Collections数据
  useEffect(() => {
    if (!initialData) {
      dispatch(fetchCollections({
        applicationId: 'app_1', // 模拟应用ID
        filters: {
          search: searchText,
          page: pagination.page,
          pageSize: pagination.pageSize,
        }
      }));
    }
  }, [dispatch, searchText, pagination.page, pagination.pageSize, initialData]);

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchText(value);
  };

  // 处理删除Collection
  const handleDelete = (collectionId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个Collection吗？此操作不可撤销。',
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await dispatch(deleteCollection(collectionId)).unwrap();
          dispatch(addNotification({
            type: 'success',
            title: '删除成功',
            message: 'Collection已成功删除',
          }));
        } catch (error) {
          dispatch(addNotification({
            type: 'error',
            title: '删除失败',
            message: error as string,
          }));
        }
      },
    });
  };

  // 处理复制Collection
  const handleDuplicate = async (collectionId: string) => {
    try {
      await dispatch(duplicateCollection({ id: collectionId })).unwrap();
      dispatch(addNotification({
        type: 'success',
        title: '复制成功',
        message: 'Collection已成功复制',
      }));
    } catch (error) {
      dispatch(addNotification({
        type: 'error',
        title: '复制失败',
        message: error as string,
      }));
    }
  };

  // 渲染Collection卡片
  const renderCollectionCard = (collection: Collection) => (
    <Col xs={24} sm={12} lg={8} xl={6} key={collection.id}>
      <CollectionCard>
        <div className="collection-header">
          <FolderOutlined className="collection-icon" />
          <Title level={5} className="collection-title">
            {collection.name}
          </Title>
          <div className="collection-actions">
            <Dropdown
              menu={{
                items: [
                  {
                    key: 'execute',
                    label: '执行',
                    icon: <PlayCircleOutlined />,
                  },
                  {
                    key: 'edit',
                    label: '编辑',
                    icon: <EditOutlined />,
                  },
                  {
                    key: 'duplicate',
                    label: '复制',
                    icon: <CopyOutlined />,
                    onClick: () => handleDuplicate(collection.id),
                  },
                  {
                    type: 'divider',
                  },
                  {
                    key: 'delete',
                    label: '删除',
                    icon: <DeleteOutlined />,
                    danger: true,
                    onClick: () => handleDelete(collection.id),
                  },
                ],
              }}
              trigger={['click']}
            >
              <Button type="text" icon={<MoreOutlined />} size="small" />
            </Dropdown>
          </div>
        </div>

        <Paragraph className="collection-description">
          {collection.description || '暂无描述'}
        </Paragraph>

        <div className="collection-meta">
          <div className="meta-item">
            <ApiOutlined />
            <span>{collection.actions.length} 个Action</span>
          </div>
          <div className="meta-item">
            <ClockCircleOutlined />
            <span>{new Date(collection.updatedAt).toLocaleDateString('zh-CN')}</span>
          </div>
        </div>

        <div className="collection-tags">
          {collection.variables.length > 0 && (
            <Tag color="blue" size="small">
              {collection.variables.length} 个变量
            </Tag>
          )}
          <Tag color="green" size="small">
            Collection
          </Tag>
        </div>
      </CollectionCard>
    </Col>
  );

  const collectionsList = Object.values(collections);

  if (loading && collectionsList.length === 0) {
    return <LoadingSpinner size="large" tip="正在加载Collections..." />;
  }

  return (
    <PageContainer>
      {/* 页面头部 */}
      <PageHeader>
        <div className="header-left">
          <Title level={2} className="page-title">数据集合</Title>
          <div className="page-description">
            管理和组织相关的API接口和数据查询，创建可重用的数据集合
          </div>
        </div>
        <div className="header-right">
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => dispatch(openModal('createCollection'))}
          >
            创建集合
          </Button>
        </div>
      </PageHeader>

      {/* 搜索过滤 */}
      <FilterSection>
        <Search
          placeholder="搜索Collection名称或描述"
          allowClear
          style={{ maxWidth: 400 }}
          onSearch={handleSearch}
          enterButton={<SearchOutlined />}
        />
      </FilterSection>

      {/* Collections网格 */}
      <CollectionGrid>
        {collectionsList.length > 0 ? (
          <Row gutter={[16, 16]}>
            {collectionsList.map(renderCollectionCard)}
          </Row>
        ) : (
          <EmptyState>
            <FolderOutlined className="empty-icon" />
            <div className="empty-title">暂无数据集合</div>
            <div className="empty-description">
              创建您的第一个数据集合，将相关的API和查询组织在一起
            </div>
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
              onClick={() => dispatch(openModal('createCollection'))}
            >
              创建集合
            </Button>
          </EmptyState>
        )}
      </CollectionGrid>
    </PageContainer>
  );
};

export default CollectionsPage;
