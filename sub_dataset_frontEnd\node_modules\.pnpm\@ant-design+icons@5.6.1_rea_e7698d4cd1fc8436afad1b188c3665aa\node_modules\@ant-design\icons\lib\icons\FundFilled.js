"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _FundFilled = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/FundFilled"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var FundFilled = function FundFilled(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _FundFilled.default
  }));
};

/**![fund](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkyNiAxNjRIOTRjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjY0MGMwIDE3LjcgMTQuMyAzMiAzMiAzMmg4MzJjMTcuNyAwIDMyLTE0LjMgMzItMzJWMTk2YzAtMTcuNy0xNC4zLTMyLTMyLTMyem0tOTIuMyAxOTQuNGwtMjk3IDI5Ny4yYTguMDMgOC4wMyAwIDAxLTExLjMgMEw0MTAuOSA1NDEuMSAyMzguNCA3MTMuN2E4LjAzIDguMDMgMCAwMS0xMS4zIDBsLTM2LjgtMzYuOGE4LjAzIDguMDMgMCAwMTAtMTEuM2wyMTQuOS0yMTVjMy4xLTMuMSA4LjItMy4xIDExLjMgMEw1MzEgNTY1bDI1NC41LTI1NC42YzMuMS0zLjEgOC4yLTMuMSAxMS4zIDBsMzYuOCAzNi44YzMuMiAzIDMuMiA4LjEuMSAxMS4yeiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(FundFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FundFilled';
}
var _default = exports.default = RefIcon;