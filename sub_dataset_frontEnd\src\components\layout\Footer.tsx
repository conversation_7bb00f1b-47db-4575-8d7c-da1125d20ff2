import React from 'react';
import { Layout, Space, Typography } from 'antd';
import styled from 'styled-components';

const { Footer: AntFooter } = Layout;
const { Text, Link } = Typography;

// 样式组件
const StyledFooter = styled(AntFooter)`
  background: var(--dataset-bg) !important;
  border-top: 1px solid var(--dataset-border);
  padding: 12px 24px;
  text-align: center;
  
  .footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    
    @media (max-width: 768px) {
      flex-direction: column;
      gap: 8px;
    }
  }
  
  .footer-left {
    color: var(--dataset-text-secondary);
    font-size: 12px;
  }
  
  .footer-right {
    display: flex;
    gap: 16px;
    
    @media (max-width: 768px) {
      gap: 12px;
    }
  }
  
  .footer-link {
    color: var(--dataset-text-secondary);
    font-size: 12px;
    
    &:hover {
      color: var(--dataset-primary);
    }
  }
`;

// Footer组件
const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  return (
    <StyledFooter>
      <div className="footer-content">
        <div className="footer-left">
          <Text>
            © {currentYear} PagePlug Dataset Manager. 数据集管理微前端应用
          </Text>
        </div>
        
        <div className="footer-right">
          <Space size="middle">
            <Link 
              href="#" 
              className="footer-link"
              onClick={(e) => e.preventDefault()}
            >
              帮助文档
            </Link>
            <Link 
              href="#" 
              className="footer-link"
              onClick={(e) => e.preventDefault()}
            >
              API文档
            </Link>
            <Link 
              href="#" 
              className="footer-link"
              onClick={(e) => e.preventDefault()}
            >
              反馈建议
            </Link>
            <Link 
              href="#" 
              className="footer-link"
              onClick={(e) => e.preventDefault()}
            >
              关于我们
            </Link>
          </Space>
        </div>
      </div>
    </StyledFooter>
  );
};

export default Footer;
