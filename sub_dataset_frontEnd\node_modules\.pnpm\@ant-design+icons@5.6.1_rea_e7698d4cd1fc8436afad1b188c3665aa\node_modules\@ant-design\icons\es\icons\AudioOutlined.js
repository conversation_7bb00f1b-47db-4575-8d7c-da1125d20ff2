import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import AudioOutlinedSvg from "@ant-design/icons-svg/es/asn/AudioOutlined";
import AntdIcon from "../components/AntdIcon";
var AudioOutlined = function AudioOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: AudioOutlinedSvg
  }));
};

/**![audio](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg0MiA0NTRjMC00LjQtMy42LTgtOC04aC02MGMtNC40IDAtOCAzLjYtOCA4IDAgMTQwLjMtMTEzLjcgMjU0LTI1NCAyNTRTMjU4IDU5NC4zIDI1OCA0NTRjMC00LjQtMy42LTgtOC04aC02MGMtNC40IDAtOCAzLjYtOCA4IDAgMTY4LjcgMTI2LjYgMzA3LjkgMjkwIDMyNy42Vjg4NEgzMjYuN2MtMTMuNyAwLTI0LjcgMTQuMy0yNC43IDMydjM2YzAgNC40IDIuOCA4IDYuMiA4aDQwNy42YzMuNCAwIDYuMi0zLjYgNi4yLTh2LTM2YzAtMTcuNy0xMS0zMi0yNC43LTMySDU0OFY3ODIuMWMxNjUuMy0xOCAyOTQtMTU4IDI5NC0zMjguMXpNNTEyIDYyNGM5My45IDAgMTcwLTc1LjIgMTcwLTE2OFYyMzJjMC05Mi44LTc2LjEtMTY4LTE3MC0xNjhzLTE3MCA3NS4yLTE3MCAxNjh2MjI0YzAgOTIuOCA3Ni4xIDE2OCAxNzAgMTY4em0tOTQtMzkyYzAtNTAuNiA0MS45LTkyIDk0LTkyczk0IDQxLjQgOTQgOTJ2MjI0YzAgNTAuNi00MS45IDkyLTk0IDkycy05NC00MS40LTk0LTkyVjIzMnoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(AudioOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'AudioOutlined';
}
export default RefIcon;