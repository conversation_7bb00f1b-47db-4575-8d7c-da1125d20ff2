import { 
  Action, 
  ActionCreateRequest, 
  ActionUpdateRequest, 
  ActionResult, 
  ValidationResult,
  ActionExecutionRequest 
} from '@/types/action';
import { ApiResponse, PaginatedResponse } from '@/types/common';
import { mockActions, mockActionResults } from '../mockData/actions';

// 模拟API延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// 模拟API响应包装器
const createApiResponse = <T>(data: T, success = true, message?: string): ApiResponse<T> => ({
  success,
  data,
  message,
  timestamp: new Date().toISOString()
});

// 模拟分页响应
const createPaginatedResponse = <T>(
  items: T[], 
  page = 1, 
  pageSize = 20
): PaginatedResponse<T> => {
  const total = items.length;
  const totalPages = Math.ceil(total / pageSize);
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedItems = items.slice(startIndex, endIndex);

  return {
    items: paginatedItems,
    total,
    page,
    pageSize,
    totalPages,
    hasNext: page < totalPages,
    hasPrev: page > 1
  };
};

export class ActionAPI {
  private actions: Action[] = [...mockActions];

  // 获取Action列表
  async getActions(
    applicationId: string,
    filters?: {
      pluginType?: string;
      actionType?: string;
      datasourceId?: string;
      search?: string;
      page?: number;
      pageSize?: number;
    }
  ): Promise<ApiResponse<PaginatedResponse<Action>>> {
    await delay(300);

    let filteredActions = this.actions.filter(action => 
      action.applicationId === applicationId
    );

    // 应用过滤器
    if (filters) {
      if (filters.pluginType) {
        filteredActions = filteredActions.filter(action => 
          action.pluginType === filters.pluginType
        );
      }
      
      if (filters.actionType) {
        filteredActions = filteredActions.filter(action => 
          action.actionType === filters.actionType
        );
      }
      
      if (filters.datasourceId) {
        filteredActions = filteredActions.filter(action => 
          action.datasourceId === filters.datasourceId
        );
      }
      
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        filteredActions = filteredActions.filter(action => 
          action.name.toLowerCase().includes(searchLower) ||
          (action.actionConfiguration.path && 
           action.actionConfiguration.path.toLowerCase().includes(searchLower))
        );
      }
    }

    const paginatedData = createPaginatedResponse(
      filteredActions,
      filters?.page || 1,
      filters?.pageSize || 20
    );

    return createApiResponse(paginatedData);
  }

  // 获取单个Action
  async getAction(id: string): Promise<ApiResponse<Action>> {
    await delay(200);

    const action = this.actions.find(a => a.id === id);
    if (!action) {
      return createApiResponse(null as any, false, `Action with id ${id} not found`);
    }

    return createApiResponse(action);
  }

  // 创建Action
  async createAction(request: ActionCreateRequest): Promise<ApiResponse<Action>> {
    await delay(500);

    const newAction: Action = {
      id: `action_${Date.now()}`,
      name: request.name,
      pluginType: request.pluginType,
      actionType: request.actionType,
      datasourceId: request.datasourceId,
      applicationId: request.applicationId,
      pageId: request.pageId,
      actionConfiguration: {
        timeout: 10000,
        ...request.actionConfiguration
      },
      executeOnLoad: false,
      isValid: true,
      invalids: [],
      jsonPathKeys: [],
      timeoutInMillisecond: request.actionConfiguration?.timeout || 10000,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      userPermissions: ['read', 'execute', 'write']
    };

    this.actions.push(newAction);
    return createApiResponse(newAction);
  }

  // 更新Action
  async updateAction(id: string, request: ActionUpdateRequest): Promise<ApiResponse<Action>> {
    await delay(400);

    const actionIndex = this.actions.findIndex(a => a.id === id);
    if (actionIndex === -1) {
      return createApiResponse(null as any, false, `Action with id ${id} not found`);
    }

    const updatedAction: Action = {
      ...this.actions[actionIndex],
      ...request,
      actionConfiguration: {
        ...this.actions[actionIndex].actionConfiguration,
        ...request.actionConfiguration
      },
      updatedAt: new Date().toISOString()
    };

    this.actions[actionIndex] = updatedAction;
    return createApiResponse(updatedAction);
  }

  // 删除Action
  async deleteAction(id: string): Promise<ApiResponse<void>> {
    await delay(300);

    const actionIndex = this.actions.findIndex(a => a.id === id);
    if (actionIndex === -1) {
      return createApiResponse(null as any, false, `Action with id ${id} not found`);
    }

    this.actions.splice(actionIndex, 1);
    return createApiResponse(undefined as any);
  }

  // 执行Action
  async executeAction(request: ActionExecutionRequest): Promise<ApiResponse<ActionResult>> {
    await delay(800);

    const action = this.actions.find(a => a.id === request.actionId);
    if (!action) {
      return createApiResponse(null as any, false, `Action with id ${request.actionId} not found`);
    }

    // 模拟执行结果
    const mockResult = mockActionResults[request.actionId];
    if (mockResult) {
      return createApiResponse(mockResult);
    }

    // 生成默认执行结果
    const defaultResult: ActionResult = {
      isExecutionSuccess: true,
      body: { message: 'Action executed successfully', data: request.params || {} },
      headers: { 'content-type': 'application/json' },
      statusCode: 200,
      isLoading: false,
      dataTypes: ['object'],
      duration: `${Math.floor(Math.random() * 1000) + 100}ms`,
      size: `${Math.floor(Math.random() * 5000) + 500}B`,
      responseDisplayFormat: 'JSON'
    };

    return createApiResponse(defaultResult);
  }

  // 验证Action配置
  async validateAction(action: Action): Promise<ApiResponse<ValidationResult>> {
    await delay(200);

    const errors: string[] = [];
    const warnings: string[] = [];

    // 基本验证
    if (!action.name || action.name.trim().length === 0) {
      errors.push('Action名称不能为空');
    }

    // 根据Action类型进行特定验证
    switch (action.actionType) {
      case 'REST_API':
        if (!action.actionConfiguration.path) {
          errors.push('API路径不能为空');
        }
        if (!action.actionConfiguration.httpMethod) {
          errors.push('HTTP方法不能为空');
        }
        break;
      
      case 'SQL':
        if (!action.actionConfiguration.query) {
          errors.push('SQL查询不能为空');
        }
        if (!action.datasourceId) {
          errors.push('数据源不能为空');
        }
        break;
      
      case 'JAVASCRIPT':
        if (!action.actionConfiguration.jsFunction) {
          errors.push('JavaScript代码不能为空');
        }
        break;
    }

    // 超时时间验证
    if (action.timeoutInMillisecond < 1000) {
      warnings.push('超时时间过短，建议至少设置为1秒');
    }
    if (action.timeoutInMillisecond > 60000) {
      warnings.push('超时时间过长，建议不超过60秒');
    }

    const result: ValidationResult = {
      isValid: errors.length === 0,
      errors,
      warnings
    };

    return createApiResponse(result);
  }

  // 复制Action
  async duplicateAction(id: string, newName?: string): Promise<ApiResponse<Action>> {
    await delay(400);

    const originalAction = this.actions.find(a => a.id === id);
    if (!originalAction) {
      return createApiResponse(null as any, false, `Action with id ${id} not found`);
    }

    const duplicatedAction: Action = {
      ...originalAction,
      id: `action_${Date.now()}`,
      name: newName || `${originalAction.name} (副本)`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    this.actions.push(duplicatedAction);
    return createApiResponse(duplicatedAction);
  }

  // 批量删除Actions
  async batchDeleteActions(ids: string[]): Promise<ApiResponse<void>> {
    await delay(600);

    this.actions = this.actions.filter(action => !ids.includes(action.id));
    return createApiResponse(undefined as any);
  }
}

// 导出单例实例
export const actionAPI = new ActionAPI();
