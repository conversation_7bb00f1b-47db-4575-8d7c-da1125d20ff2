import { configureStore } from '@reduxjs/toolkit';
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';

import { actionSlice } from './slices/actionSlice';
import { collectionSlice } from './slices/collectionSlice';
import { executionSlice } from './slices/executionSlice';
import { uiSlice } from './slices/uiSlice';
import { apiMiddleware } from './middleware/apiMiddleware';
import { executionMiddleware } from './middleware/executionMiddleware';

// 配置Store
export const store = configureStore({
  reducer: {
    actions: actionSlice.reducer,
    collections: collectionSlice.reducer,
    execution: executionSlice.reducer,
    ui: uiSlice.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        // 忽略这些action类型的序列化检查
        ignoredActions: [
          'execution/executeAction/pending',
          'execution/executeCollection/pending',
        ],
        // 忽略这些路径的序列化检查
        ignoredPaths: [
          'execution.executionContext',
          'actions.currentAction.actionConfiguration.body',
        ],
      },
      // 启用不可变性检查
      immutableCheck: {
        warnAfter: 128,
      },
    }).concat(apiMiddleware, executionMiddleware),
  devTools: process.env.NODE_ENV !== 'production',
});

// 导出类型
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// 导出类型化的hooks
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

// 导出选择器
export const selectActions = (state: RootState) => state.actions;
export const selectCollections = (state: RootState) => state.collections;
export const selectExecution = (state: RootState) => state.execution;
export const selectUI = (state: RootState) => state.ui;

// 复合选择器
export const selectCurrentAction = (state: RootState) => state.actions.currentAction;
export const selectCurrentCollection = (state: RootState) => state.collections.currentCollection;
export const selectActionById = (id: string) => (state: RootState) => 
  state.actions.items[id];
export const selectCollectionById = (id: string) => (state: RootState) => 
  state.collections.items[id];

// 过滤选择器
export const selectFilteredActions = (state: RootState) => {
  const { items, filters } = state.actions;
  const actionList = Object.values(items);
  
  if (!filters || Object.keys(filters).length === 0) {
    return actionList;
  }
  
  return actionList.filter(action => {
    if (filters.pluginType && action.pluginType !== filters.pluginType) {
      return false;
    }
    
    if (filters.actionType && action.actionType !== filters.actionType) {
      return false;
    }
    
    if (filters.datasourceId && action.datasourceId !== filters.datasourceId) {
      return false;
    }
    
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      const nameMatch = action.name.toLowerCase().includes(searchLower);
      const pathMatch = action.actionConfiguration.path?.toLowerCase().includes(searchLower);
      if (!nameMatch && !pathMatch) {
        return false;
      }
    }
    
    return true;
  });
};

export const selectFilteredCollections = (state: RootState) => {
  const { items, filters } = state.collections;
  const collectionList = Object.values(items);
  
  if (!filters || Object.keys(filters).length === 0) {
    return collectionList;
  }
  
  return collectionList.filter(collection => {
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      const nameMatch = collection.name.toLowerCase().includes(searchLower);
      const descMatch = collection.description?.toLowerCase().includes(searchLower);
      if (!nameMatch && !descMatch) {
        return false;
      }
    }
    
    return true;
  });
};

// 执行状态选择器
export const selectActionExecutionState = (actionId: string) => (state: RootState) => ({
  isExecuting: state.execution.executingActions.includes(actionId),
  result: state.execution.results[actionId],
  error: state.execution.errors[actionId],
});

export const selectCollectionExecutionState = (collectionId: string) => (state: RootState) => ({
  isExecuting: state.execution.executingCollections.includes(collectionId),
  result: state.execution.collectionResults[collectionId],
  error: state.execution.collectionErrors[collectionId],
});

// 统计选择器
export const selectActionStats = (state: RootState) => {
  const actions = Object.values(state.actions.items);
  
  return {
    total: actions.length,
    byPluginType: actions.reduce((acc, action) => {
      acc[action.pluginType] = (acc[action.pluginType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
    byActionType: actions.reduce((acc, action) => {
      acc[action.actionType] = (acc[action.actionType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
    valid: actions.filter(action => action.isValid).length,
    invalid: actions.filter(action => !action.isValid).length,
    executeOnLoad: actions.filter(action => action.executeOnLoad).length,
  };
};

export const selectCollectionStats = (state: RootState) => {
  const collections = Object.values(state.collections.items);
  
  return {
    total: collections.length,
    totalActions: collections.reduce((sum, collection) => sum + collection.actions.length, 0),
    avgActionsPerCollection: collections.length > 0 
      ? collections.reduce((sum, collection) => sum + collection.actions.length, 0) / collections.length 
      : 0,
    withVariables: collections.filter(collection => collection.variables.length > 0).length,
  };
};

// Store初始化函数
export const initializeStore = () => {
  // 这里可以添加Store初始化逻辑
  // 比如从localStorage恢复状态等
  
  return store;
};

// 清理Store状态
export const resetStore = () => {
  store.dispatch({ type: 'RESET_STORE' });
};

export default store;
