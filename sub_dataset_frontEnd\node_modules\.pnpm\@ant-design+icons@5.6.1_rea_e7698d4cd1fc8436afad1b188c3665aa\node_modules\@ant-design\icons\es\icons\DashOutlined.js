import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import DashOutlinedSvg from "@ant-design/icons-svg/es/asn/DashOutlined";
import AntdIcon from "../components/AntdIcon";
var DashOutlined = function DashOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: DashOutlinedSvg
  }));
};

/**![dash](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTExMiA0NzZoMTYwdjcySDExMnptMzIwIDBoMTYwdjcySDQzMnptMzIwIDBoMTYwdjcySDc1MnoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(DashOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'DashOutlined';
}
export default RefIcon;