// 数据集Action相关类型定义

export enum PluginType {
  API = 'API',
  DB = 'DB',
  JS = 'JS',
  GRAPHQL = 'GRAPHQL',
}

export enum ActionType {
  REST_API = 'REST_API',
  GRAPHQL = 'GRAPHQL',
  SQL = 'SQL',
  NOSQL = 'NOSQL',
  JAVASCRIPT = 'JAVASCRIPT',
}

export enum HttpMethod {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  DELETE = 'DELETE',
  PATCH = 'PATCH',
  HEAD = 'HEAD',
  OPTIONS = 'OPTIONS',
}

export interface Parameter {
  key: string;
  value: string;
  description?: string;
  type?: 'string' | 'number' | 'boolean' | 'object';
  required?: boolean;
}

export interface Header {
  key: string;
  value: string;
  description?: string;
}

export interface ActionConfiguration {
  // REST API配置
  httpMethod?: HttpMethod;
  path?: string;
  headers?: Header[];
  queryParameters?: Parameter[];
  body?: string;
  bodyType?: 'json' | 'form-data' | 'raw' | 'x-www-form-urlencoded';
  
  // GraphQL配置
  query?: string;
  variables?: Record<string, any>;
  
  // SQL配置
  query?: string;
  datasourceId?: string;
  
  // JavaScript配置
  jsFunction?: string;
  jsArguments?: Parameter[];
  
  // 通用配置
  timeout?: number;
  pagination?: {
    enabled: boolean;
    pageSize?: number;
    pageParam?: string;
    offsetParam?: string;
  };
}

export interface Action {
  id: string;
  name: string;
  pluginType: PluginType;
  actionType: ActionType;
  datasourceId?: string;
  applicationId: string;
  pageId?: string;
  actionConfiguration: ActionConfiguration;
  executeOnLoad: boolean;
  isValid: boolean;
  invalids: string[];
  jsonPathKeys: string[];
  timeoutInMillisecond: number;
  createdAt: string;
  updatedAt: string;
  userPermissions: string[];
}

export interface ActionResult {
  isExecutionSuccess: boolean;
  body: any;
  headers: Record<string, string>;
  statusCode?: number;
  isLoading: boolean;
  dataTypes: any[];
  duration: string;
  size: string;
  responseDisplayFormat: string;
  readableError?: string;
  errorType?: string;
  pluginErrorDetails?: any;
}

export interface ActionCreateRequest {
  name: string;
  pluginType: PluginType;
  actionType: ActionType;
  datasourceId?: string;
  applicationId: string;
  pageId?: string;
  actionConfiguration: Partial<ActionConfiguration>;
}

export interface ActionUpdateRequest {
  name?: string;
  actionConfiguration?: Partial<ActionConfiguration>;
  executeOnLoad?: boolean;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface ExecutionContext {
  params?: Record<string, any>;
  globals?: Record<string, any>;
  appsmith?: Record<string, any>;
}

export interface ActionExecutionRequest {
  actionId: string;
  params?: Record<string, any>;
  context?: ExecutionContext;
}
