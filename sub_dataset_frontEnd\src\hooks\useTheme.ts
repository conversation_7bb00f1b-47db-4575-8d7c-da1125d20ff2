import { useEffect, useState, useCallback } from 'react';

import { DatasetTheme, lightTheme, darkTheme, generateCSSVariables } from '@/styles/theme';

// 模拟事件总线，实际项目中应该从主应用获取
interface EventBus {
  on: (event: string, callback: Function) => void;
  off: (event: string, callback: Function) => void;
  emit: (event: string, data?: any) => void;
}

// 简单的事件总线实现
const createEventBus = (): EventBus => {
  const events: Record<string, Function[]> = {};
  
  return {
    on: (event: string, callback: Function) => {
      if (!events[event]) {
        events[event] = [];
      }
      events[event].push(callback);
    },
    off: (event: string, callback: Function) => {
      if (events[event]) {
        events[event] = events[event].filter(cb => cb !== callback);
      }
    },
    emit: (event: string, data?: any) => {
      if (events[event]) {
        events[event].forEach(callback => callback(data));
      }
    },
  };
};

const eventBus = createEventBus();

export interface ThemeConfig {
  mode: 'light' | 'dark';
  primaryColor?: string;
  customColors?: Partial<DatasetTheme['colors']>;
}

export const useTheme = () => {
  const [themeConfig, setThemeConfig] = useState<ThemeConfig>({
    mode: 'light',
  });
  
  const [currentTheme, setCurrentTheme] = useState<DatasetTheme>(lightTheme);

  // 更新CSS变量
  const updateCSSVariables = useCallback((theme: DatasetTheme) => {
    const root = document.documentElement;
    const variables = generateCSSVariables(theme);
    
    Object.entries(variables).forEach(([key, value]) => {
      root.style.setProperty(key, value);
    });
  }, []);

  // 生成主题对象
  const generateTheme = useCallback((config: ThemeConfig): DatasetTheme => {
    const baseTheme = config.mode === 'dark' ? darkTheme : lightTheme;
    
    if (!config.primaryColor && !config.customColors) {
      return baseTheme;
    }
    
    const customTheme: DatasetTheme = {
      ...baseTheme,
      colors: {
        ...baseTheme.colors,
        ...(config.customColors || {}),
        ...(config.primaryColor ? { primary: config.primaryColor } : {}),
      },
    };
    
    return customTheme;
  }, []);

  // 切换主题模式
  const toggleTheme = useCallback(() => {
    setThemeConfig(prev => ({
      ...prev,
      mode: prev.mode === 'light' ? 'dark' : 'light',
    }));
  }, []);

  // 设置主色调
  const setPrimaryColor = useCallback((color: string) => {
    setThemeConfig(prev => ({
      ...prev,
      primaryColor: color,
    }));
  }, []);

  // 设置自定义颜色
  const setCustomColors = useCallback((colors: Partial<DatasetTheme['colors']>) => {
    setThemeConfig(prev => ({
      ...prev,
      customColors: {
        ...prev.customColors,
        ...colors,
      },
    }));
  }, []);

  // 重置主题
  const resetTheme = useCallback(() => {
    setThemeConfig({
      mode: 'light',
    });
  }, []);

  // 监听主应用主题变化
  useEffect(() => {
    const handleThemeChange = (newConfig: ThemeConfig) => {
      setThemeConfig(newConfig);
    };

    // 监听主应用主题变化事件
    eventBus.on('theme:changed', handleThemeChange);

    // 获取初始主题配置
    eventBus.emit('theme:get', (initialConfig: ThemeConfig) => {
      if (initialConfig) {
        setThemeConfig(initialConfig);
      }
    });

    return () => {
      eventBus.off('theme:changed', handleThemeChange);
    };
  }, []);

  // 当主题配置变化时，更新主题对象和CSS变量
  useEffect(() => {
    const newTheme = generateTheme(themeConfig);
    setCurrentTheme(newTheme);
    updateCSSVariables(newTheme);
  }, [themeConfig, generateTheme, updateCSSVariables]);

  // 保存主题配置到localStorage
  useEffect(() => {
    try {
      localStorage.setItem('dataset-theme-config', JSON.stringify(themeConfig));
    } catch (error) {
      console.warn('Failed to save theme config to localStorage:', error);
    }
  }, [themeConfig]);

  // 从localStorage加载主题配置
  useEffect(() => {
    try {
      const savedConfig = localStorage.getItem('dataset-theme-config');
      if (savedConfig) {
        const parsedConfig = JSON.parse(savedConfig);
        setThemeConfig(parsedConfig);
      }
    } catch (error) {
      console.warn('Failed to load theme config from localStorage:', error);
    }
  }, []);

  return {
    theme: currentTheme,
    themeConfig,
    toggleTheme,
    setPrimaryColor,
    setCustomColors,
    resetTheme,
    isDark: themeConfig.mode === 'dark',
    isLight: themeConfig.mode === 'light',
  };
};

// 主题上下文Hook（用于在组件中获取主题）
export const useThemeContext = () => {
  const { theme } = useTheme();
  return theme;
};

// 响应式断点Hook
export const useBreakpoint = () => {
  const [breakpoint, setBreakpoint] = useState<'xs' | 'sm' | 'md' | 'lg' | 'xl'>('md');

  useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth;
      if (width < 576) {
        setBreakpoint('xs');
      } else if (width < 768) {
        setBreakpoint('sm');
      } else if (width < 992) {
        setBreakpoint('md');
      } else if (width < 1200) {
        setBreakpoint('lg');
      } else {
        setBreakpoint('xl');
      }
    };

    updateBreakpoint();
    window.addEventListener('resize', updateBreakpoint);

    return () => {
      window.removeEventListener('resize', updateBreakpoint);
    };
  }, []);

  return {
    breakpoint,
    isXs: breakpoint === 'xs',
    isSm: breakpoint === 'sm',
    isMd: breakpoint === 'md',
    isLg: breakpoint === 'lg',
    isXl: breakpoint === 'xl',
    isMobile: breakpoint === 'xs' || breakpoint === 'sm',
    isTablet: breakpoint === 'md',
    isDesktop: breakpoint === 'lg' || breakpoint === 'xl',
  };
};
