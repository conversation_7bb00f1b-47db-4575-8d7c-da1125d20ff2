import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import CompassTwoToneSvg from "@ant-design/icons-svg/es/asn/CompassTwoTone";
import AntdIcon from "../components/AntdIcon";
var CompassTwoTone = function CompassTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: CompassTwoToneSvg
  }));
};

/**![compass](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiAxNDBjLTIwNS40IDAtMzcyIDE2Ni42LTM3MiAzNzJzMTY2LjYgMzcyIDM3MiAzNzIgMzcyLTE2Ni42IDM3Mi0zNzItMTY2LjYtMzcyLTM3Mi0zNzJ6TTMyNy42IDcwMS43Yy0yIC45LTQuNCAwLTUuMy0yLjEtLjQtMS0uNC0yLjIgMC0zLjJMNDIxIDQ3MC45IDU1My4xIDYwM2wtMjI1LjUgOTguN3ptMzc1LjEtMzc1LjFMNjA0IDU1Mi4xIDQ3MS45IDQyMGwyMjUuNS05OC43YzItLjkgNC40IDAgNS4zIDIuMS40IDEgLjQgMi4xIDAgMy4yeiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNMzIyLjMgNjk2LjRjLS40IDEtLjQgMi4yIDAgMy4yLjkgMi4xIDMuMyAzIDUuMyAyLjFMNTUzLjEgNjAzIDQyMSA0NzAuOWwtOTguNyAyMjUuNXptMzc1LjEtMzc1LjFMNDcxLjkgNDIwIDYwNCA1NTIuMWw5OC43LTIyNS41Yy40LTEuMS40LTIuMiAwLTMuMi0uOS0yLjEtMy4zLTMtNS4zLTIuMXoiIGZpbGw9IiMxNjc3ZmYiIC8+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0wIDgyMGMtMjA1LjQgMC0zNzItMTY2LjYtMzcyLTM3MnMxNjYuNi0zNzIgMzcyLTM3MiAzNzIgMTY2LjYgMzcyIDM3Mi0xNjYuNiAzNzItMzcyIDM3MnoiIGZpbGw9IiMxNjc3ZmYiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(CompassTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CompassTwoTone';
}
export default RefIcon;