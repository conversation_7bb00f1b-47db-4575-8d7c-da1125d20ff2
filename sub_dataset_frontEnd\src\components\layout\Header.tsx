import React from 'react';
import { Layout, <PERSON><PERSON>, Space, Tooltip, Badge } from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  BellOutlined,
  SettingOutlined,
  QuestionCircleOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
} from '@ant-design/icons';
import styled from 'styled-components';

import { useAppDispatch, useAppSelector } from '@/store';
import { toggleSidebar, openDrawer } from '@/store/slices/uiSlice';

const { Header: AntHeader } = Layout;

// 样式组件
const StyledHeader = styled(AntHeader)`
  background: var(--dataset-bg) !important;
  border-bottom: 1px solid var(--dataset-border);
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
  
  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;
  }
  
  .header-right {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .header-button {
    border: none;
    background: transparent;
    color: var(--dataset-text);
    
    &:hover {
      background: var(--dataset-bg-secondary);
      color: var(--dataset-primary);
    }
  }
`;

const BreadcrumbSection = styled.div`
  color: var(--dataset-text-secondary);
  font-size: 14px;
  
  .breadcrumb-item {
    color: var(--dataset-text);
    
    &:not(:last-child)::after {
      content: ' / ';
      color: var(--dataset-text-secondary);
      margin: 0 8px;
    }
  }
`;

// Header组件
const Header: React.FC = () => {
  const dispatch = useAppDispatch();
  const { sidebarCollapsed, notifications } = useAppSelector(state => state.ui);
  const [isFullscreen, setIsFullscreen] = React.useState(false);

  // 切换侧边栏
  const handleToggleSidebar = () => {
    dispatch(toggleSidebar());
  };

  // 切换全屏
  const handleToggleFullscreen = () => {
    if (!isFullscreen) {
      document.documentElement.requestFullscreen?.();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen?.();
      setIsFullscreen(false);
    }
  };

  // 监听全屏状态变化
  React.useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  // 获取未读通知数量
  const unreadNotifications = notifications.filter(n => !n.id.includes('read')).length;

  return (
    <StyledHeader>
      <div className="header-left">
        <Button
          type="text"
          icon={sidebarCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
          onClick={handleToggleSidebar}
          className="header-button"
        />
        
        <BreadcrumbSection>
          <span className="breadcrumb-item">数据集管理</span>
          <span className="breadcrumb-item">API管理</span>
        </BreadcrumbSection>
      </div>

      <div className="header-right">
        <Space size="small">
          <Tooltip title="全屏">
            <Button
              type="text"
              icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
              onClick={handleToggleFullscreen}
              className="header-button"
            />
          </Tooltip>

          <Tooltip title="通知">
            <Badge count={unreadNotifications} size="small">
              <Button
                type="text"
                icon={<BellOutlined />}
                onClick={() => dispatch(openDrawer('help'))}
                className="header-button"
              />
            </Badge>
          </Tooltip>

          <Tooltip title="帮助">
            <Button
              type="text"
              icon={<QuestionCircleOutlined />}
              onClick={() => dispatch(openDrawer('help'))}
              className="header-button"
            />
          </Tooltip>

          <Tooltip title="设置">
            <Button
              type="text"
              icon={<SettingOutlined />}
              onClick={() => dispatch(openDrawer('help'))}
              className="header-button"
            />
          </Tooltip>
        </Space>
      </div>
    </StyledHeader>
  );
};

export default Header;
