import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import AppstoreTwoToneSvg from "@ant-design/icons-svg/es/asn/AppstoreTwoTone";
import AntdIcon from "../components/AntdIcon";
var AppstoreTwoTone = function AppstoreTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: AppstoreTwoToneSvg
  }));
};

/**![appstore](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg2NCAxNDRINTYwYy04LjggMC0xNiA3LjItMTYgMTZ2MzA0YzAgOC44IDcuMiAxNiAxNiAxNmgzMDRjOC44IDAgMTYtNy4yIDE2LTE2VjE2MGMwLTguOC03LjItMTYtMTYtMTZ6bS01MiAyNjhINjEyVjIxMmgyMDB2MjAwek00NjQgNTQ0SDE2MGMtOC44IDAtMTYgNy4yLTE2IDE2djMwNGMwIDguOCA3LjIgMTYgMTYgMTZoMzA0YzguOCAwIDE2LTcuMiAxNi0xNlY1NjBjMC04LjgtNy4yLTE2LTE2LTE2em0tNTIgMjY4SDIxMlY2MTJoMjAwdjIwMHptNTItNjY4SDE2MGMtOC44IDAtMTYgNy4yLTE2IDE2djMwNGMwIDguOCA3LjIgMTYgMTYgMTZoMzA0YzguOCAwIDE2LTcuMiAxNi0xNlYxNjBjMC04LjgtNy4yLTE2LTE2LTE2em0tNTIgMjY4SDIxMlYyMTJoMjAwdjIwMHptNDUyIDEzMkg1NjBjLTguOCAwLTE2IDcuMi0xNiAxNnYzMDRjMCA4LjggNy4yIDE2IDE2IDE2aDMwNGM4LjggMCAxNi03LjIgMTYtMTZWNTYwYzAtOC44LTcuMi0xNi0xNi0xNnptLTUyIDI2OEg2MTJWNjEyaDIwMHYyMDB6IiBmaWxsPSIjMTY3N2ZmIiAvPjxwYXRoIGQ9Ik0yMTIgMjEyaDIwMHYyMDBIMjEyem00MDAgMGgyMDB2MjAwSDYxMnpNMjEyIDYxMmgyMDB2MjAwSDIxMnptNDAwIDBoMjAwdjIwMEg2MTJ6IiBmaWxsPSIjZTZmNGZmIiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(AppstoreTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'AppstoreTwoTone';
}
export default RefIcon;