import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import BlockOutlinedSvg from "@ant-design/icons-svg/es/asn/BlockOutlined";
import AntdIcon from "../components/AntdIcon";
var BlockOutlined = function BlockOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: BlockOutlinedSvg
  }));
};

/**![block](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg1NiAzNzZINjQ4VjE2OGMwLTguOC03LjItMTYtMTYtMTZIMTY4Yy04LjggMC0xNiA3LjItMTYgMTZ2NDY0YzAgOC44IDcuMiAxNiAxNiAxNmgyMDh2MjA4YzAgOC44IDcuMiAxNiAxNiAxNmg0NjRjOC44IDAgMTYtNy4yIDE2LTE2VjM5MmMwLTguOC03LjItMTYtMTYtMTZ6bS00ODAgMTZ2MTg4SDIyMFYyMjBoMzYwdjE1NkgzOTJjLTguOCAwLTE2IDcuMi0xNiAxNnptMjA0IDUydjEzNkg0NDRWNDQ0aDEzNnptMjI0IDM2MEg0NDRWNjQ4aDE4OGM4LjggMCAxNi03LjIgMTYtMTZWNDQ0aDE1NnYzNjB6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(BlockOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'BlockOutlined';
}
export default RefIcon;