import React, { useEffect } from 'react';
import { notification } from 'antd';
import { 
  CheckCircleOutlined, 
  ExclamationCircleOutlined, 
  InfoCircleOutlined, 
  CloseCircleOutlined 
} from '@ant-design/icons';

import { useAppSelector, useAppDispatch } from '@/store';
import { removeNotification } from '@/store/slices/uiSlice';

// 通知图标映射
const getNotificationIcon = (type: string) => {
  switch (type) {
    case 'success':
      return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
    case 'warning':
      return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;
    case 'error':
      return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
    case 'info':
    default:
      return <InfoCircleOutlined style={{ color: '#1890ff' }} />;
  }
};

// 通知提供者组件
const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const dispatch = useAppDispatch();
  const { notifications } = useAppSelector(state => state.ui);
  const [api, contextHolder] = notification.useNotification();

  // 监听通知变化并显示
  useEffect(() => {
    notifications.forEach(notificationItem => {
      // 检查是否已经显示过这个通知
      if (!notificationItem.id.includes('shown')) {
        const { id, type, title, message, duration = 4.5 } = notificationItem;
        
        api[type as keyof typeof api]({
          key: id,
          message: title,
          description: message,
          icon: getNotificationIcon(type),
          duration,
          placement: 'topRight',
          onClose: () => {
            dispatch(removeNotification(id));
          },
        });

        // 标记为已显示
        notificationItem.id = `${id}_shown`;
      }
    });
  }, [notifications, api, dispatch]);

  // 配置全局通知样式
  useEffect(() => {
    notification.config({
      placement: 'topRight',
      duration: 4.5,
      maxCount: 5,
      rtl: false,
    });
  }, []);

  return (
    <>
      {contextHolder}
      {children}
    </>
  );
};

export default NotificationProvider;
