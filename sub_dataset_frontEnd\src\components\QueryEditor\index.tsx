import React from 'react';
import { Card, Typography, Button } from 'antd';
import { DatabaseOutlined } from '@ant-design/icons';
import styled from 'styled-components';

const { Title, Paragraph } = Typography;

const EditorContainer = styled(Card)`
  text-align: center;
  max-width: 600px;
  margin: 60px auto;
  
  .editor-icon {
    font-size: 64px;
    color: var(--dataset-success);
    margin-bottom: 24px;
  }
  
  .editor-title {
    margin-bottom: 16px;
  }
  
  .editor-description {
    color: var(--dataset-text-secondary);
    margin-bottom: 32px;
  }
`;

interface QueryEditorProps {
  queryId?: string;
  queryType?: 'sql' | 'nosql' | 'graphql';
  onSave?: (data: any) => void;
  onCancel?: () => void;
}

const QueryEditor: React.FC<QueryEditorProps> = ({ queryId, queryType, onSave, onCancel }) => {
  return (
    <EditorContainer>
      <DatabaseOutlined className="editor-icon" />
      <Title level={2} className="editor-title">
        查询编辑器
      </Title>
      <Paragraph className="editor-description">
        查询编辑器组件正在开发中，敬请期待！
        <br />
        您将能够使用Monaco Editor编写SQL、NoSQL和GraphQL查询。
      </Paragraph>
      <Button type="primary" disabled>
        开始编写查询（即将推出）
      </Button>
    </EditorContainer>
  );
};

export default QueryEditor;
