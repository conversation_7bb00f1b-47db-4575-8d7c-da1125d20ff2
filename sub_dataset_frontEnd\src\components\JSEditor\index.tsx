import React from 'react';
import { Card, Typography, Button } from 'antd';
import { CodeOutlined } from '@ant-design/icons';
import styled from 'styled-components';

const { Title, Paragraph } = Typography;

const EditorContainer = styled(Card)`
  text-align: center;
  max-width: 600px;
  margin: 60px auto;
  
  .editor-icon {
    font-size: 64px;
    color: var(--dataset-warning);
    margin-bottom: 24px;
  }
  
  .editor-title {
    margin-bottom: 16px;
  }
  
  .editor-description {
    color: var(--dataset-text-secondary);
    margin-bottom: 32px;
  }
`;

interface JSEditorProps {
  jsObjectId?: string;
  onSave?: (data: any) => void;
  onCancel?: () => void;
}

const JSEditor: React.FC<JSEditorProps> = ({ jsObjectId, onSave, onCancel }) => {
  return (
    <EditorContainer>
      <CodeOutlined className="editor-icon" />
      <Title level={2} className="editor-title">
        JavaScript编辑器
      </Title>
      <Paragraph className="editor-description">
        JavaScript编辑器组件正在开发中，敬请期待！
        <br />
        您将能够编写和调试JavaScript函数，用于数据处理和业务逻辑。
      </Paragraph>
      <Button type="primary" disabled>
        开始编写代码（即将推出）
      </Button>
    </EditorContainer>
  );
};

export default JSEditor;
