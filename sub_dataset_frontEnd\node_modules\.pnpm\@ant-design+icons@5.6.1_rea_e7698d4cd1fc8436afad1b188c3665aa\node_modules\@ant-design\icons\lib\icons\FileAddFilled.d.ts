import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![file-add](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQ4MCA1ODBIMzcyYTggOCAwIDAwLTggOHY0OGE4IDggMCAwMDggOGgxMDh2MTA4YTggOCAwIDAwOCA4aDQ4YTggOCAwIDAwOC04VjY0NGgxMDhhOCA4IDAgMDA4LTh2LTQ4YTggOCAwIDAwLTgtOEg1NDRWNDcyYTggOCAwIDAwLTgtOGgtNDhhOCA4IDAgMDAtOCA4djEwOHptMzc0LjYtMjkxLjNjNiA2IDkuNCAxNC4xIDkuNCAyMi42VjkyOGMwIDE3LjctMTQuMyAzMi0zMiAzMkgxOTJjLTE3LjcgMC0zMi0xNC4zLTMyLTMyVjk2YzAtMTcuNyAxNC4zLTMyIDMyLTMyaDQyNC43YzguNSAwIDE2LjcgMy40IDIyLjcgOS40bDIxNS4yIDIxNS4zek03OTAuMiAzMjZMNjAyIDEzNy44VjMyNmgxODguMnoiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
