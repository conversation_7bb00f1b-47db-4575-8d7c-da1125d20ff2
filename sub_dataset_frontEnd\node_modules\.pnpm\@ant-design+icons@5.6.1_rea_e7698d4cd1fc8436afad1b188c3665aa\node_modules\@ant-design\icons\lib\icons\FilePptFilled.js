"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _FilePptFilled = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/FilePptFilled"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var FilePptFilled = function FilePptFilled(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _FilePptFilled.default
  }));
};

/**![file-ppt](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg1NC42IDI4OC43YzYgNiA5LjQgMTQuMSA5LjQgMjIuNlY5MjhjMCAxNy43LTE0LjMgMzItMzIgMzJIMTkyYy0xNy43IDAtMzItMTQuMy0zMi0zMlY5NmMwLTE3LjcgMTQuMy0zMiAzMi0zMmg0MjQuN2M4LjUgMCAxNi43IDMuNCAyMi43IDkuNGwyMTUuMiAyMTUuM3pNNzkwLjIgMzI2TDYwMiAxMzcuOFYzMjZoMTg4LjJ6TTQ2OC41MyA3NjB2LTkxLjU0aDU5LjI3YzYwLjU3IDAgMTAwLjItMzkuNjUgMTAwLjItOTguMTIgMC01OC4yMi0zOS41OC05OC4zNC05OS45OC05OC4zNEg0MjRhMTIgMTIgMCAwMC0xMiAxMnYyNzZhMTIgMTIgMCAwMDEyIDEyaDMyLjUzYTEyIDEyIDAgMDAxMi0xMnptMC0xMzkuMzNoMzQuOWM0Ny44MiAwIDY3LjE5LTEyLjkzIDY3LjE5LTUwLjMzIDAtMzIuMDUtMTguMTItNTAuMTItNDkuODctNTAuMTJoLTUyLjIydjEwMC40NXoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(FilePptFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FilePptFilled';
}
var _default = exports.default = RefIcon;