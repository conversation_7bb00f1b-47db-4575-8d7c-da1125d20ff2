# 安装指南

## 依赖安装问题解决方案

如果您遇到依赖安装错误，请按照以下步骤操作：

### 1. 清理缓存

```bash
# 如果使用 npm
npm cache clean --force

# 如果使用 pnpm
pnpm store prune

# 如果使用 yarn
yarn cache clean
```

### 2. 删除现有的 node_modules 和锁文件

```bash
# Windows
rmdir /s node_modules
del package-lock.json
del pnpm-lock.yaml
del yarn.lock

# macOS/Linux
rm -rf node_modules
rm -f package-lock.json pnpm-lock.yaml yarn.lock
```

### 3. 使用推荐的包管理器安装

我们推荐使用 **npm** 来安装依赖：

```bash
npm install
```

如果您更喜欢使用 pnpm，请确保使用最新版本：

```bash
# 更新 pnpm 到最新版本
npm install -g pnpm@latest

# 然后安装依赖
pnpm install
```

### 4. 如果仍然有问题，尝试逐步安装

```bash
# 先安装核心依赖
npm install react react-dom react-router-dom

# 安装状态管理
npm install @reduxjs/toolkit react-redux

# 安装 UI 组件库
npm install antd @ant-design/icons styled-components

# 安装构建工具
npm install -D webpack webpack-cli webpack-dev-server html-webpack-plugin ts-loader

# 安装 TypeScript 相关
npm install -D typescript @types/react @types/react-dom

# 安装样式相关
npm install -D css-loader style-loader postcss postcss-loader tailwindcss autoprefixer

# 安装代码质量工具
npm install -D eslint prettier @typescript-eslint/eslint-plugin @typescript-eslint/parser eslint-plugin-react eslint-plugin-react-hooks eslint-config-prettier
```

### 5. 启动开发服务器

```bash
npm run dev
```

应用将在 http://localhost:3002 启动

## 常见问题

### Q: 遇到 "Module not found" 错误
A: 确保所有依赖都已正确安装，并检查 import 路径是否正确。

### Q: TypeScript 编译错误
A: 运行 `npm run type-check` 检查类型错误，并根据提示修复。

### Q: 样式不生效
A: 确保 TailwindCSS 配置正确，并且 CSS 文件已正确导入。

### Q: 端口被占用
A: 修改 webpack.config.js 中的端口号，或者停止占用 3002 端口的其他进程。

## 最小化依赖版本

如果您想要最小化的依赖安装，可以只安装以下核心依赖：

```json
{
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-router-dom": "^6.15.0",
    "@reduxjs/toolkit": "^1.9.5",
    "react-redux": "^8.1.2",
    "antd": "^5.8.6",
    "styled-components": "^6.0.7",
    "@ant-design/icons": "^5.2.6"
  },
  "devDependencies": {
    "@types/react": "^18.2.21",
    "@types/react-dom": "^18.2.7",
    "typescript": "^5.2.2",
    "webpack": "^5.88.2",
    "webpack-cli": "^5.1.4",
    "webpack-dev-server": "^4.15.1",
    "html-webpack-plugin": "^5.5.3",
    "ts-loader": "^9.4.4",
    "css-loader": "^6.8.1",
    "style-loader": "^3.3.3"
  }
}
```

## 技术支持

如果您仍然遇到问题，请：

1. 检查 Node.js 版本（推荐 16.x 或更高）
2. 检查网络连接和 npm 镜像源
3. 尝试使用不同的包管理器（npm、yarn、pnpm）
4. 查看详细的错误日志并搜索解决方案

## 镜像源配置

如果在中国大陆，建议配置 npm 镜像源：

```bash
# 设置淘宝镜像
npm config set registry https://registry.npmmirror.com/

# 或者使用 cnpm
npm install -g cnpm --registry=https://registry.npmmirror.com/
cnpm install
```
