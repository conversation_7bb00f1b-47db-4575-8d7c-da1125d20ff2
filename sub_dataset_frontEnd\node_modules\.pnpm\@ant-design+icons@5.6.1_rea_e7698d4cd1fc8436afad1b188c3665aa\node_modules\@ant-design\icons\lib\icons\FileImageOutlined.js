"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _FileImageOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/FileImageOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var FileImageOutlined = function FileImageOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _FileImageOutlined.default
  }));
};

/**![file-image](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTU1My4xIDUwOS4xbC03Ny44IDk5LjItNDEuMS01Mi40YTggOCAwIDAwLTEyLjYgMGwtOTkuOCAxMjcuMmE3Ljk4IDcuOTggMCAwMDYuMyAxMi45SDY5NmM2LjcgMCAxMC40LTcuNyA2LjMtMTIuOWwtMTM2LjUtMTc0YTguMSA4LjEgMCAwMC0xMi43IDB6TTM2MCA0NDJhNDAgNDAgMCAxMDgwIDAgNDAgNDAgMCAxMC04MCAwem00OTQuNi0xNTMuNEw2MzkuNCA3My40Yy02LTYtMTQuMS05LjQtMjIuNi05LjRIMTkyYy0xNy43IDAtMzIgMTQuMy0zMiAzMnY4MzJjMCAxNy43IDE0LjMgMzIgMzIgMzJoNjQwYzE3LjcgMCAzMi0xNC4zIDMyLTMyVjMxMS4zYzAtOC41LTMuNC0xNi43LTkuNC0yMi43ek03OTAuMiAzMjZINjAyVjEzNy44TDc5MC4yIDMyNnptMS44IDU2MkgyMzJWMTM2aDMwMnYyMTZhNDIgNDIgMCAwMDQyIDQyaDIxNnY0OTR6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(FileImageOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FileImageOutlined';
}
var _default = exports.default = RefIcon;