{"name": "pageplug-dataset-app", "version": "1.0.0", "description": "PagePlug数据集管理微前端应用", "main": "src/index.tsx", "scripts": {"dev": "webpack serve --mode development", "build": "webpack --mode production", "preview": "npx serve dist -s -l 3002", "lint": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "lint:check": "eslint src --ext .js,.jsx,.ts,.tsx", "type-check": "tsc --noEmit", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "clean": "rm -rf dist"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.15.0", "@reduxjs/toolkit": "^1.9.5", "react-redux": "^8.1.2", "antd": "^5.8.6", "styled-components": "^6.0.7", "@ant-design/icons": "^5.2.6", "axios": "^1.5.0", "dayjs": "^1.11.9", "lodash-es": "^4.17.21"}, "devDependencies": {"@types/react": "^18.2.21", "@types/react-dom": "^18.2.7", "@types/lodash-es": "^4.17.9", "@types/styled-components": "^5.1.26", "typescript": "^5.2.2", "webpack": "^5.88.2", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1", "html-webpack-plugin": "^5.5.3", "ts-loader": "^9.4.4", "style-loader": "^3.3.3", "css-loader": "^6.8.1", "postcss": "^8.4.29", "postcss-loader": "^7.3.3", "tailwindcss": "^3.3.3", "@tailwindcss/forms": "^0.5.6", "@tailwindcss/typography": "^0.5.10", "autoprefixer": "^10.4.15", "eslint": "^8.48.0", "@typescript-eslint/eslint-plugin": "^6.5.0", "@typescript-eslint/parser": "^6.5.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-config-prettier": "^9.0.0", "prettier": "^3.0.3"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/pageplug-dataset-app.git"}, "keywords": ["react", "typescript", "microfrontend", "module-federation", "styled-components", "tailwindcss", "antd", "dataset", "pageplug"], "author": "PagePlug Team", "license": "MIT", "packageManager": "pnpm@10.12.4+sha512.5ea8b0deed94ed68691c9bad4c955492705c5eeb8a87ef86bc62c74a26b037b08ff9570f108b2e4dbd1dd1a9186fea925e527f141c648e85af45631074680184"}