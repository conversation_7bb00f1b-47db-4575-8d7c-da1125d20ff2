"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _FileUnknownFilled = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/FileUnknownFilled"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var FileUnknownFilled = function FileUnknownFilled(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _FileUnknownFilled.default
  }));
};

/**![file-unknown](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg1NC42IDI4OC43YzYgNiA5LjQgMTQuMSA5LjQgMjIuNlY5MjhjMCAxNy43LTE0LjMgMzItMzIgMzJIMTkyYy0xNy43IDAtMzItMTQuMy0zMi0zMlY5NmMwLTE3LjcgMTQuMy0zMiAzMi0zMmg0MjQuN2M4LjUgMCAxNi43IDMuNCAyMi43IDkuNGwyMTUuMiAyMTUuM3pNNzkwLjIgMzI2TDYwMiAxMzcuOFYzMjZoMTg4LjJ6TTQwMiA1NDljMCA1LjQgNC40IDkuNSA5LjggOS41aDMyLjRjNS40IDAgOS44LTQuMiA5LjgtOS40IDAtMjguMiAyNS44LTUxLjYgNTgtNTEuNnM1OCAyMy40IDU4IDUxLjVjMCAyNS4zLTIxIDQ3LjItNDkuMyA1MC45LTE5LjMgMi44LTM0LjUgMjAuMy0zNC43IDQwLjF2MzJjMCA1LjUgNC41IDEwIDEwIDEwaDMyYzUuNSAwIDEwLTQuNSAxMC0xMHYtMTIuMmMwLTYgNC0xMS41IDkuNy0xMy4zIDQ0LjYtMTQuNCA3NS01NCA3NC4zLTk4LjktLjgtNTUuNS00OS4yLTEwMC44LTEwOC41LTEwMS42LTYxLjQtLjctMTExLjUgNDUuNi0xMTEuNSAxMDN6bTExMCAyMjdhMzIgMzIgMCAxMDAtNjQgMzIgMzIgMCAwMDAgNjR6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(FileUnknownFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FileUnknownFilled';
}
var _default = exports.default = RefIcon;