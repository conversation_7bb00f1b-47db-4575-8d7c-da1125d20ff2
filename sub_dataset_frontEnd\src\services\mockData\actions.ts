import { Action, ActionType, PluginType, HttpMethod, ActionResult } from '@/types/action';

// 模拟Action数据
export const mockActions: Action[] = [
  {
    id: 'action_1',
    name: '获取用户列表',
    pluginType: PluginType.API,
    actionType: ActionType.REST_API,
    datasourceId: 'datasource_1',
    applicationId: 'app_1',
    pageId: 'page_1',
    actionConfiguration: {
      httpMethod: HttpMethod.GET,
      path: '/api/users',
      headers: [
        { key: 'Content-Type', value: 'application/json' },
        { key: 'Authorization', value: 'Bearer {{appsmith.store.token}}' }
      ],
      queryParameters: [
        { key: 'page', value: '{{Table1.pageNo}}', description: '页码', type: 'number' },
        { key: 'limit', value: '{{Table1.pageSize}}', description: '每页数量', type: 'number' },
        { key: 'search', value: '{{SearchInput.text}}', description: '搜索关键词', type: 'string' }
      ],
      timeout: 10000,
      pagination: {
        enabled: true,
        pageSize: 20,
        pageParam: 'page',
        offsetParam: 'offset'
      }
    },
    executeOnLoad: true,
    isValid: true,
    invalids: [],
    jsonPathKeys: ['data', 'users'],
    timeoutInMillisecond: 10000,
    createdAt: '2024-01-15T10:30:00Z',
    updatedAt: '2024-01-20T14:45:00Z',
    userPermissions: ['read', 'execute']
  },
  {
    id: 'action_2',
    name: '创建用户',
    pluginType: PluginType.API,
    actionType: ActionType.REST_API,
    datasourceId: 'datasource_1',
    applicationId: 'app_1',
    pageId: 'page_1',
    actionConfiguration: {
      httpMethod: HttpMethod.POST,
      path: '/api/users',
      headers: [
        { key: 'Content-Type', value: 'application/json' },
        { key: 'Authorization', value: 'Bearer {{appsmith.store.token}}' }
      ],
      body: JSON.stringify({
        name: '{{UserForm.name}}',
        email: '{{UserForm.email}}',
        role: '{{UserForm.role}}',
        department: '{{UserForm.department}}'
      }, null, 2),
      bodyType: 'json',
      timeout: 15000
    },
    executeOnLoad: false,
    isValid: true,
    invalids: [],
    jsonPathKeys: ['data'],
    timeoutInMillisecond: 15000,
    createdAt: '2024-01-15T11:00:00Z',
    updatedAt: '2024-01-18T09:20:00Z',
    userPermissions: ['read', 'execute', 'write']
  },
  {
    id: 'action_3',
    name: '查询订单统计',
    pluginType: PluginType.DB,
    actionType: ActionType.SQL,
    datasourceId: 'datasource_2',
    applicationId: 'app_1',
    pageId: 'page_2',
    actionConfiguration: {
      query: `SELECT 
  DATE(created_at) as order_date,
  COUNT(*) as order_count,
  SUM(total_amount) as total_revenue,
  AVG(total_amount) as avg_order_value
FROM orders 
WHERE created_at >= '{{DatePicker1.selectedDate}}'
  AND created_at <= '{{DatePicker2.selectedDate}}'
  AND status IN ({{StatusFilter.selectedOptions}})
GROUP BY DATE(created_at)
ORDER BY order_date DESC
LIMIT {{Table2.pageSize}} OFFSET {{Table2.pageOffset}};`,
      datasourceId: 'datasource_2',
      timeout: 30000
    },
    executeOnLoad: true,
    isValid: true,
    invalids: [],
    jsonPathKeys: ['data'],
    timeoutInMillisecond: 30000,
    createdAt: '2024-01-16T08:15:00Z',
    updatedAt: '2024-01-22T16:30:00Z',
    userPermissions: ['read', 'execute']
  },
  {
    id: 'action_4',
    name: '用户数据处理',
    pluginType: PluginType.JS,
    actionType: ActionType.JAVASCRIPT,
    applicationId: 'app_1',
    pageId: 'page_1',
    actionConfiguration: {
      jsFunction: `export default {
  // 处理用户数据
  processUserData: async () => {
    const users = await GetUsers.run();
    
    return users.data.map(user => ({
      ...user,
      fullName: \`\${user.firstName} \${user.lastName}\`,
      isActive: user.status === 'active',
      avatar: user.avatar || '/default-avatar.png',
      displayRole: user.role.charAt(0).toUpperCase() + user.role.slice(1)
    }));
  },
  
  // 验证用户输入
  validateUserForm: () => {
    const { name, email, role } = UserForm.data;
    const errors = {};
    
    if (!name || name.trim().length < 2) {
      errors.name = '姓名至少需要2个字符';
    }
    
    if (!email || !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email)) {
      errors.email = '请输入有效的邮箱地址';
    }
    
    if (!role) {
      errors.role = '请选择用户角色';
    }
    
    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  },
  
  // 格式化数据用于导出
  formatDataForExport: (data) => {
    return data.map(item => ({
      'ID': item.id,
      '姓名': item.name,
      '邮箱': item.email,
      '角色': item.role,
      '部门': item.department,
      '状态': item.status === 'active' ? '激活' : '禁用',
      '创建时间': new Date(item.createdAt).toLocaleDateString('zh-CN')
    }));
  }
};`,
      timeout: 5000
    },
    executeOnLoad: false,
    isValid: true,
    invalids: [],
    jsonPathKeys: [],
    timeoutInMillisecond: 5000,
    createdAt: '2024-01-17T13:45:00Z',
    updatedAt: '2024-01-21T11:10:00Z',
    userPermissions: ['read', 'execute', 'write']
  },
  {
    id: 'action_5',
    name: '获取产品信息',
    pluginType: PluginType.API,
    actionType: ActionType.GRAPHQL,
    datasourceId: 'datasource_3',
    applicationId: 'app_1',
    pageId: 'page_3',
    actionConfiguration: {
      query: `query GetProducts($first: Int!, $after: String, $filter: ProductFilter) {
  products(first: $first, after: $after, filter: $filter) {
    edges {
      node {
        id
        name
        description
        price
        category {
          id
          name
        }
        images {
          url
          alt
        }
        inventory {
          quantity
          isInStock
        }
        createdAt
        updatedAt
      }
    }
    pageInfo {
      hasNextPage
      hasPreviousPage
      startCursor
      endCursor
    }
    totalCount
  }
}`,
      variables: {
        first: '{{Table3.pageSize}}',
        after: '{{Table3.cursor}}',
        filter: {
          category: '{{CategoryFilter.selectedOption}}',
          priceRange: {
            min: '{{PriceFilter.minValue}}',
            max: '{{PriceFilter.maxValue}}'
          },
          search: '{{SearchInput.text}}'
        }
      },
      timeout: 20000
    },
    executeOnLoad: true,
    isValid: true,
    invalids: [],
    jsonPathKeys: ['data', 'products'],
    timeoutInMillisecond: 20000,
    createdAt: '2024-01-18T09:30:00Z',
    updatedAt: '2024-01-23T15:20:00Z',
    userPermissions: ['read', 'execute']
  }
];

// 模拟Action执行结果
export const mockActionResults: Record<string, ActionResult> = {
  action_1: {
    isExecutionSuccess: true,
    body: {
      data: [
        {
          id: 1,
          name: '张三',
          email: '<EMAIL>',
          role: 'admin',
          department: '技术部',
          status: 'active',
          createdAt: '2024-01-10T08:00:00Z'
        },
        {
          id: 2,
          name: '李四',
          email: '<EMAIL>',
          role: 'user',
          department: '市场部',
          status: 'active',
          createdAt: '2024-01-12T10:30:00Z'
        },
        {
          id: 3,
          name: '王五',
          email: '<EMAIL>',
          role: 'user',
          department: '销售部',
          status: 'inactive',
          createdAt: '2024-01-14T14:15:00Z'
        }
      ],
      pagination: {
        page: 1,
        pageSize: 20,
        total: 156,
        totalPages: 8
      }
    },
    headers: {
      'content-type': 'application/json',
      'x-total-count': '156'
    },
    statusCode: 200,
    isLoading: false,
    dataTypes: ['object'],
    duration: '245ms',
    size: '1.2KB',
    responseDisplayFormat: 'JSON'
  },
  action_3: {
    isExecutionSuccess: true,
    body: {
      data: [
        {
          order_date: '2024-01-23',
          order_count: 45,
          total_revenue: 12580.50,
          avg_order_value: 279.57
        },
        {
          order_date: '2024-01-22',
          order_count: 38,
          total_revenue: 9876.30,
          avg_order_value: 259.90
        },
        {
          order_date: '2024-01-21',
          order_count: 52,
          total_revenue: 15234.80,
          avg_order_value: 292.98
        }
      ]
    },
    headers: {
      'content-type': 'application/json'
    },
    statusCode: 200,
    isLoading: false,
    dataTypes: ['object'],
    duration: '1.2s',
    size: '856B',
    responseDisplayFormat: 'TABLE'
  }
};
