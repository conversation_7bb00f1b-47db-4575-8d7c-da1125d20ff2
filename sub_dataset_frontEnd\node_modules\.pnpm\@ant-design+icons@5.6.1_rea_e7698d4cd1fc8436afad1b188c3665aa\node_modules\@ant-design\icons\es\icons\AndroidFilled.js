import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import AndroidFilledSvg from "@ant-design/icons-svg/es/asn/AndroidFilled";
import AntdIcon from "../components/AntdIcon";
var AndroidFilled = function AndroidFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: AndroidFilledSvg
  }));
};

/**![android](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI3MC4xIDc0MS43YzAgMjMuNCAxOS4xIDQyLjUgNDIuNiA0Mi41aDQ4Ljd2MTIwLjRjMCAzMC41IDI0LjUgNTUuNCA1NC42IDU1LjQgMzAuMiAwIDU0LjYtMjQuOCA1NC42LTU1LjRWNzg0LjFoODV2MTIwLjRjMCAzMC41IDI0LjUgNTUuNCA1NC42IDU1LjQgMzAuMiAwIDU0LjYtMjQuOCA1NC42LTU1LjRWNzg0LjFoNDguN2MyMy41IDAgNDIuNi0xOS4xIDQyLjYtNDIuNVYzNDYuNGgtNDg2djM5NS4zem0zNTcuMS02MDAuMWw0NC45LTY1YzIuNi0zLjggMi04LjktMS41LTExLjQtMy41LTIuNC04LjUtMS4yLTExLjEgMi42bC00Ni42IDY3LjZjLTMwLjctMTIuMS02NC45LTE4LjgtMTAwLjgtMTguOC0zNS45IDAtNzAuMSA2LjctMTAwLjggMTguOGwtNDYuNi02Ny41Yy0yLjYtMy44LTcuNi01LjEtMTEuMS0yLjYtMy41IDIuNC00LjEgNy40LTEuNSAxMS40bDQ0LjkgNjVjLTcxLjQgMzMuMi0xMjEuNCA5Ni4xLTEyNy44IDE2OS42aDQ4NmMtNi42LTczLjYtNTYuNy0xMzYuNS0xMjgtMTY5Ljd6TTQwOS41IDI0NC4xYTI2LjkgMjYuOSAwIDExMjYuOS0yNi45IDI2Ljk3IDI2Ljk3IDAgMDEtMjYuOSAyNi45em0yMDguNCAwYTI2LjkgMjYuOSAwIDExMjYuOS0yNi45IDI2Ljk3IDI2Ljk3IDAgMDEtMjYuOSAyNi45em0yMjMuNCAxMDAuN2MtMzAuMiAwLTU0LjYgMjQuOC01NC42IDU1LjR2MjE2LjRjMCAzMC41IDI0LjUgNTUuNCA1NC42IDU1LjQgMzAuMiAwIDU0LjYtMjQuOCA1NC42LTU1LjRWNDAwLjFjLjEtMzAuNi0yNC4zLTU1LjMtNTQuNi01NS4zem0tNjU4LjYgMGMtMzAuMiAwLTU0LjYgMjQuOC01NC42IDU1LjR2MjE2LjRjMCAzMC41IDI0LjUgNTUuNCA1NC42IDU1LjQgMzAuMiAwIDU0LjYtMjQuOCA1NC42LTU1LjRWNDAwLjFjMC0zMC42LTI0LjUtNTUuMy01NC42LTU1LjN6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(AndroidFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'AndroidFilled';
}
export default RefIcon;