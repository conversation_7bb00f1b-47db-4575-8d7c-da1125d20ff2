import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import CreditCardOutlinedSvg from "@ant-design/icons-svg/es/asn/CreditCardOutlined";
import AntdIcon from "../components/AntdIcon";
var CreditCardOutlined = function CreditCardOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: CreditCardOutlinedSvg
  }));
};

/**![credit-card](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkyOCAxNjBIOTZjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjY0MGMwIDE3LjcgMTQuMyAzMiAzMiAzMmg4MzJjMTcuNyAwIDMyLTE0LjMgMzItMzJWMTkyYzAtMTcuNy0xNC4zLTMyLTMyLTMyem0tNzkyIDcyaDc1MnYxMjBIMTM2VjIzMnptNzUyIDU2MEgxMzZWNDQwaDc1MnYzNTJ6bS0yMzctNjRoMTY1YzQuNCAwIDgtMy42IDgtOHYtNzJjMC00LjQtMy42LTgtOC04SDY1MWMtNC40IDAtOCAzLjYtOCA4djcyYzAgNC40IDMuNiA4IDggOHoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(CreditCardOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CreditCardOutlined';
}
export default RefIcon;