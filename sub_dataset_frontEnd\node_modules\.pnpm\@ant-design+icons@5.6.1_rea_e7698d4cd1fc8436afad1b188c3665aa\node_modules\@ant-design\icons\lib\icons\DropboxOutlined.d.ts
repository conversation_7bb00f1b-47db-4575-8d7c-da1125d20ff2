import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![dropbox](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTY0IDU1Ni45bDI2NC4yIDE3My41TDUxMi41IDU3NyAyNDYuOCA0MTIuN3ptODk2LTI5MC4zem0wIDBMNjk2LjggOTUgNTEyLjUgMjQ4LjVsMjY1LjIgMTY0LjJMNTEyLjUgNTc3bDE4NC4zIDE1My40TDk2MCA1NTguOCA3NzcuNyA0MTIuN3pNNTEzIDYwOS44TDMyOC4yIDc2My4zbC03OS40LTUxLjV2NTcuOEw1MTMgOTI4bDI2My43LTE1OC40di01Ny44bC03OC45IDUxLjV6TTMyOC4yIDk1TDY0IDI2NS4xbDE4Mi44IDE0Ny42IDI2NS43LTE2NC4yek02NCA1NTYuOXoiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
