import React, { lazy, Suspense } from 'react';
import { Routes, Route, Navigate, useLocation, useNavigate } from 'react-router-dom';

import MainLayout from '@/components/layout/MainLayout';
import LoadingSpinner from '@/components/shared/LoadingSpinner';
import { LoadingContainer } from '@/styles/global';

// 懒加载页面组件
const ActionsPage = lazy(() => import('@/pages/ActionsPage'));
const ActionDetailPage = lazy(() => import('@/pages/ActionDetailPage'));
const CollectionsPage = lazy(() => import('@/pages/CollectionsPage'));
const CollectionDetailPage = lazy(() => import('@/pages/CollectionDetailPage'));
const QueryBuilderPage = lazy(() => import('@/pages/QueryBuilderPage'));
const JSObjectsPage = lazy(() => import('@/pages/JSObjectsPage'));
const JSObjectDetailPage = lazy(() => import('@/pages/JSObjectDetailPage'));
const TemplatesPage = lazy(() => import('@/pages/TemplatesPage'));
const SettingsPage = lazy(() => import('@/pages/SettingsPage'));
const NotFoundPage = lazy(() => import('@/pages/NotFoundPage'));

// 页面加载组件
const PageLoading: React.FC = () => (
  <LoadingContainer>
    <LoadingSpinner size="large" tip="正在加载页面..." />
  </LoadingContainer>
);

// 路由配置
interface AppRoutesProps {
  initialData?: any;
  onNavigate?: (path: string) => void;
}

const AppRoutes: React.FC<AppRoutesProps> = ({ initialData, onNavigate }) => {
  const location = useLocation();
  const navigate = useNavigate();
  
  // 如果提供了onNavigate回调，监听路由变化并调用它
  React.useEffect(() => {
    if (onNavigate) {
      onNavigate(location.pathname);
    }
  }, [location.pathname, onNavigate]);
  
  return (
    <Suspense fallback={<PageLoading />}>
      <Routes>
        {/* 重定向根路径到actions */}
        <Route path="/" element={<Navigate to="/actions" replace />} />
        
        {/* 主布局路由 */}
        <Route path="/" element={<MainLayout />}>
          {/* Actions路由 */}
          <Route path="actions" element={<ActionsPage initialData={initialData?.actions} />} />
          <Route path="actions/:actionId" element={<ActionDetailPage />} />
          
          {/* Collections路由 */}
          <Route path="collections" element={<CollectionsPage initialData={initialData?.collections} />} />
          <Route path="collections/:collectionId" element={<CollectionDetailPage />} />
          
          {/* Query Builder路由 */}
          <Route path="query-builder" element={<QueryBuilderPage />} />
          <Route path="query-builder/:queryId" element={<QueryBuilderPage />} />
          
          {/* JS Objects路由 */}
          <Route path="js-objects" element={<JSObjectsPage />} />
          <Route path="js-objects/:jsObjectId" element={<JSObjectDetailPage />} />
          
          {/* Templates路由 */}
          <Route path="templates" element={<TemplatesPage />} />
          <Route path="templates/:templateId" element={<TemplatesPage />} />
          
          {/* Settings路由 */}
          <Route path="settings" element={<SettingsPage />} />
          <Route path="settings/:section" element={<SettingsPage />} />
        </Route>
        
        {/* 404页面 */}
        <Route path="*" element={<NotFoundPage />} />
      </Routes>
    </Suspense>
  );
};

// 导出路由配置
export default AppRoutes;

// 导出路由列表，用于微前端集成
export const routes = [
  {
    path: '/actions',
    name: 'Actions',
    icon: 'api',
    component: ActionsPage,
  },
  {
    path: '/collections',
    name: 'Collections',
    icon: 'folder',
    component: CollectionsPage,
  },
  {
    path: '/query-builder',
    name: 'Query Builder',
    icon: 'database',
    component: QueryBuilderPage,
  },
  {
    path: '/js-objects',
    name: 'JS Objects',
    icon: 'code',
    component: JSObjectsPage,
  },
  {
    path: '/templates',
    name: 'Templates',
    icon: 'template',
    component: TemplatesPage,
  },
  {
    path: '/settings',
    name: 'Settings',
    icon: 'setting',
    component: SettingsPage,
  },
];

// 导出路由工具函数
export const getRouteByPath = (path: string) => {
  return routes.find(route => route.path === path);
};

export const getRouteByName = (name: string) => {
  return routes.find(route => route.name === name);
};
